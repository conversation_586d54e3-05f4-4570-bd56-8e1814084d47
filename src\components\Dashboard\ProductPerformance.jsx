import React, { useState } from 'react';
import {
  Box,
  Grid,
  GridItem,
  Text,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  HStack,
  Select,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Tooltip,
  Icon,
  Progress,
  useColorModeValue,
} from '@chakra-ui/react';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiAlertCircle,
  FiPackage,
  FiDollarSign,
  FiShoppingCart,
} from 'react-icons/fi';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

const ProductPerformance = () => {
  const [timeRange, setTimeRange] = useState('7days');
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Sample data - In a real app, this would come from your API
  const performanceData = {
    metrics: [
      {
        label: 'Total Sales',
        value: 12458,
        change: 23.1,
        icon: FiShoppingCart,
        prediction: 13500,
      },
      {
        label: 'In Stock',
        value: 1876,
        change: -5.3,
        icon: FiPackage,
        prediction: 1700,
      },
      {
        label: 'Average Price',
        value: 89.99,
        change: 2.5,
        icon: FiDollarSign,
        prediction: 94.99,
      },
    ],
    trending: [
      { name: 'Mon', sales: 4000, prediction: 4100 },
      { name: 'Tue', sales: 3000, prediction: 3200 },
      { name: 'Wed', sales: 2000, prediction: 2300 },
      { name: 'Thu', sales: 2780, prediction: 2900 },
      { name: 'Fri', sales: 1890, prediction: 2100 },
      { name: 'Sat', sales: 2390, prediction: 2500 },
      { name: 'Sun', sales: 3490, prediction: 3600 },
    ],
    products: [
      {
        id: 1,
        name: 'Premium Headphones',
        sku: 'HD-PRO-001',
        stock: 234,
        sold: 567,
        price: 199.99,
        status: 'optimal',
        prediction: 'increase',
      },
      {
        id: 2,
        name: 'Wireless Earbuds',
        sku: 'WE-BUD-002',
        stock: 45,
        sold: 890,
        price: 149.99,
        status: 'low_stock',
        prediction: 'stable',
      },
      {
        id: 3,
        name: 'Smart Watch',
        sku: 'SW-PRO-003',
        stock: 12,
        sold: 345,
        price: 299.99,
        status: 'critical',
        prediction: 'decrease',
      },
    ],
  };

  const getStatusColor = (status) => {
    const colors = {
      optimal: 'green',
      low_stock: 'orange',
      critical: 'red',
    };
    return colors[status] || 'gray';
  };

  const getPredictionIcon = (prediction) => {
    switch (prediction) {
      case 'increase':
        return <Icon as={FiTrendingUp} color="green.500" />;
      case 'decrease':
        return <Icon as={FiTrendingDown} color="red.500" />;
      default:
        return null;
    }
  };

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack justify="space-between" mb={6}>
        <Heading size="md">Product Performance & Predictions</Heading>
        <Select
          width="200px"
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
        >
          <option value="7days">Last 7 Days</option>
          <option value="30days">Last 30 Days</option>
          <option value="90days">Last 90 Days</option>
        </Select>
      </HStack>

      {/* Key Metrics */}
      <Grid templateColumns="repeat(3, 1fr)" gap={4} mb={6}>
        {performanceData.metrics.map((metric, index) => (
          <GridItem key={index}>
            <Stat p={4} bg={useColorModeValue('gray.50', 'gray.700')} borderRadius="md">
              <HStack spacing={2}>
                <Icon as={metric.icon} color="blue.500" boxSize={5} />
                <StatLabel>{metric.label}</StatLabel>
              </HStack>
              <StatNumber>
                {typeof metric.value === 'number' && metric.label.includes('Price')
                  ? `$${metric.value}`
                  : metric.value.toLocaleString()}
              </StatNumber>
              <StatHelpText>
                <HStack spacing={2}>
                  <StatArrow type={metric.change > 0 ? 'increase' : 'decrease'} />
                  <Text>{Math.abs(metric.change)}%</Text>
                  <Tooltip label="Predicted value for next period">
                    <Text color="gray.500" fontSize="sm">
                      Prediction: {typeof metric.prediction === 'number' && metric.label.includes('Price')
                        ? `$${metric.prediction}`
                        : metric.prediction.toLocaleString()}
                    </Text>
                  </Tooltip>
                </HStack>
              </StatHelpText>
            </Stat>
          </GridItem>
        ))}
      </Grid>

      {/* Sales Trend Chart */}
      <Box mb={6} height="300px">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={performanceData.trending}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <RechartsTooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="sales"
              stroke="#3182ce"
              name="Actual Sales"
            />
            <Line
              type="monotone"
              dataKey="prediction"
              stroke="#805ad5"
              strokeDasharray="5 5"
              name="Predicted Sales"
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>

      {/* Products Table */}
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Product</Th>
            <Th>SKU</Th>
            <Th isNumeric>In Stock</Th>
            <Th isNumeric>Sold</Th>
            <Th isNumeric>Price</Th>
            <Th>Status</Th>
            <Th>Prediction</Th>
          </Tr>
        </Thead>
        <Tbody>
          {performanceData.products.map((product) => (
            <Tr key={product.id}>
              <Td fontWeight="medium">{product.name}</Td>
              <Td>{product.sku}</Td>
              <Td isNumeric>{product.stock}</Td>
              <Td isNumeric>{product.sold}</Td>
              <Td isNumeric>${product.price}</Td>
              <Td>
                <Badge colorScheme={getStatusColor(product.status)}>
                  {product.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </Td>
              <Td>
                <HStack spacing={2}>
                  {getPredictionIcon(product.prediction)}
                  <Text>{product.prediction.charAt(0).toUpperCase() + product.prediction.slice(1)}</Text>
                </HStack>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default ProductPerformance;