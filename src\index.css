:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Enable smooth scrolling for better mobile experience */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

body {
  min-height: 100vh;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-attachment: fixed;
  overscroll-behavior-y: none; /* Prevents pull-to-refresh on mobile */
}

/* Allow text selection for specific elements */
input, 
textarea, 
[contenteditable="true"],
.selectable-text {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Enhance touch feedback */
button, 
a, 
.clickable {
  cursor: pointer;
  touch-action: manipulation;
}

/* Improve mobile touch targets */
@media (max-width: 768px) {
  button, 
  a, 
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px -8px rgba(0, 0, 0, 0.15);
}

.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (prefers-color-scheme: dark) {
  .card {
    background: rgba(26, 32, 44, 0.8);
  }
}

@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

.app-container {
  min-height: 100vh;
  position: relative;
}

main {
  padding-bottom: 60px; /* Ensure content doesn't get hidden behind the AI button */
}



