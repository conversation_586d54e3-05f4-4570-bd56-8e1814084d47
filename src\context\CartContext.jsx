import { createContext, useContext, useState } from 'react';

const CartContext = createContext();

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState([]);

  const addToCart = (product) => {
    setCart(prevCart => {
      // Create unique key for item based on product ID and variant ID
      const itemKey = `${product.id}-${product.variant?.id || 'no-variant'}`;
      
      const existingItemIndex = prevCart.findIndex(item => 
        `${item.id}-${item.variant?.id || 'no-variant'}` === itemKey
      );

      if (existingItemIndex >= 0) {
        // Update existing item
        const newCart = [...prevCart];
        newCart[existingItemIndex] = {
          ...newCart[existingItemIndex],
          quantity: newCart[existingItemIndex].quantity + product.quantity
        };
        return newCart;
      }

      // Add new item
      return [...prevCart, product];
    });
  };

  const removeFromCart = (productId, variantId = null) => {
    setCart(prevCart => 
      prevCart.filter(item => {
        if (variantId) {
          return !(item.id === productId && item.variant?.id === variantId);
        }
        return item.id !== productId;
      })
    );
  };

  const updateQuantity = (productId, variantId, newQuantity) => {
    setCart(prevCart =>
      prevCart.map(item => {
        if (item.id === productId && item.variant?.id === variantId) {
          return { ...item, quantity: parseInt(newQuantity) };
        }
        return item;
      })
    );
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const clearCart = () => {
    setCart([]);
  };

  return (
    <CartContext.Provider value={{
      cart,
      addToCart,
      removeFromCart,
      updateQuantity,
      getCartTotal,
      clearCart,
    }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};



