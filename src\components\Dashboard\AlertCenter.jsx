import {
  Box, VStack, HStack, Text, Icon, Badge,
  useColorModeValue, Divider
} from '@chakra-ui/react';
import { FiAlertCircle, FiBell, FiInfo } from 'react-icons/fi';

const AlertCenter = () => {
  const alerts = [
    {
      id: 1,
      type: 'error',
      message: 'System performance degradation detected',
      time: '2 minutes ago',
      icon: FiAlertCircle,
    },
    {
      id: 2,
      type: 'warning',
      message: 'Database backup scheduled in 30 minutes',
      time: '5 minutes ago',
      icon: FiBell,
    },
    {
      id: 3,
      type: 'info',
      message: 'New feature deployment completed',
      time: '10 minutes ago',
      icon: FiInfo,
    },
  ];

  const getAlertColor = (type) => {
    switch (type) {
      case 'error': return 'red';
      case 'warning': return 'orange';
      case 'info': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <Box
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="lg"
      p={4}
      boxShadow="sm"
    >
      <VStack spacing={4} align="stretch">
        {alerts.map((alert, index) => (
          <Box key={alert.id}>
            <HStack spacing={3}>
              <Icon
                as={alert.icon}
                color={`${getAlertColor(alert.type)}.500`}
                boxSize={5}
              />
              <VStack align="start" spacing={0} flex={1}>
                <Text fontSize="sm" fontWeight="medium">
                  {alert.message}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {alert.time}
                </Text>
              </VStack>
              <Badge colorScheme={getAlertColor(alert.type)}>
                {alert.type}
              </Badge>
            </HStack>
            {index < alerts.length - 1 && <Divider mt={3} />}
          </Box>
        ))}
      </VStack>
    </Box>
  );
};

export default AlertCenter;