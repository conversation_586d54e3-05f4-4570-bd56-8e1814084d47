import {
  Box, VStack, HStack, Text, Icon, Badge,
  useColorModeValue, Button
} from '@chakra-ui/react';
import { FiAlertTriangle, FiInfo, FiCheckCircle } from 'react-icons/fi';

const AlertItem = ({ type, message, time }) => {
  const icons = {
    error: FiAlertTriangle,
    warning: FiAlertTriangle,
    info: FiInfo,
    success: FiCheckCircle
  };

  const colors = {
    error: 'red',
    warning: 'orange',
    info: 'blue',
    success: 'green'
  };

  return (
    <HStack
      w="full"
      p={3}
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="md"
      borderLeft="4px solid"
      borderLeftColor={`${colors[type]}.400`}
      _hover={{ transform: 'translateX(2px)', transition: 'transform 0.2s' }}
    >
      <Icon as={icons[type]} color={`${colors[type]}.400`} boxSize={5} />
      <Box flex={1}>
        <Text fontSize="sm" fontWeight="medium">{message}</Text>
        <Text fontSize="xs" color="gray.500">{time}</Text>
      </Box>
      <Badge colorScheme={colors[type]}>{type}</Badge>
    </HStack>
  );
};

const SystemAlerts = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.700');

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="xl"
      boxShadow="sm"
    >
      <HStack justify="space-between" mb={4}>
        <Text fontSize="lg" fontWeight="medium">System Alerts</Text>
        <Button size="sm" variant="ghost">View All</Button>
      </HStack>
      
      <VStack spacing={3} align="stretch">
        <AlertItem
          type="error"
          message="Database connection timeout"
          time="2 minutes ago"
        />
        <AlertItem
          type="warning"
          message="High memory usage detected"
          time="15 minutes ago"
        />
        <AlertItem
          type="info"
          message="System update available"
          time="1 hour ago"
        />
        <AlertItem
          type="success"
          message="Backup completed successfully"
          time="2 hours ago"
        />
      </VStack>
    </Box>
  );
};

export default SystemAlerts;