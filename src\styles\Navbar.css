.navbar {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar-logo {
  font-weight: 700;
  font-size: 1.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: transform 0.2s ease;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

.navbar-logo img {
  width: 40px;
  height: 40px;
  transition: transform 0.3s ease;
}

.navbar-logo:hover img {
  transform: rotate(10deg);
}

.nav-link {
  position: relative;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: width 0.2s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-link.active {
  background: rgba(66, 153, 225, 0.1);
  color: var(--chakra-colors-blue-500);
}

.mobile-menu-button {
  display: none;
}

@media (max-width: 768px) {
  .mobile-menu-button {
    display: block;
  }
  
  .navbar-logo {
    font-size: 1.25rem;
  }
  
  .navbar-logo img {
    width: 32px;
    height: 32px;
  }
}

@media (prefers-color-scheme: dark) {
  .navbar {
    background: rgba(26, 32, 44, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Animation for mobile menu */
.mobile-menu-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.mobile-menu-exit {
  opacity: 1;
  transform: translateX(0);
}

.mobile-menu-exit-active {
  opacity: 0;
  transform: translateX(-100%);
  transition: opacity 300ms, transform 300ms;
}

