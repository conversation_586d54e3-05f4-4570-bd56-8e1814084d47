import { useState } from 'react'
import { useNavigate, Link as RouterLink } from 'react-router-dom'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Text,
  FormErrorMessage,
  useToast,
  Link,
} from '@chakra-ui/react'
import { authStyles as styles } from '../styles/auth'
import useAuthStore from '../store/authStore'
import Loading from './Loading'
import '../styles/Register.css'

const RegisterSchema = Yup.object().shape({
  username: Yup.string()
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('Required'),
  email: Yup.string()
    .email('Invalid email')
    .required('Required'),
  password: Yup.string()
    .min(6, 'Too Short!')
    .required('Required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Required'),
})

function Register() {
  const [isLoading, setIsLoading] = useState(false)
  const navigate = useNavigate()
  const toast = useToast()
  const { register } = useAuthStore()

  const handleSubmit = async (values) => {
    setIsLoading(true)
    try {
      await register(values)
      navigate('/dashboard')
    } catch (error) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 3000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return <Loading message="Creating your account..." />
  }

  return (
    <Box {...styles.container} className="register-container">
      <VStack spacing={8}>
        <Text {...styles.header}>Register</Text>
        <Formik
          initialValues={{
            username: '',
            email: '',
            password: '',
            confirmPassword: '',
          }}
          validationSchema={RegisterSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched }) => (
            <Form className="register-form">
              <VStack {...styles.formStack}>
                <FormControl 
                  isInvalid={errors.username && touched.username}
                  className="form-field"
                >
                  <FormLabel>Username</FormLabel>
                  <Field
                    as={Input}
                    name="username"
                    placeholder="Enter your username"
                  />
                  <FormErrorMessage className="error-message">
                    {errors.username}
                  </FormErrorMessage>
                </FormControl>

                <FormControl 
                  isInvalid={errors.email && touched.email}
                  className="form-field"
                >
                  <FormLabel>Email</FormLabel>
                  <Field
                    as={Input}
                    name="email"
                    type="email"
                    placeholder="Enter your email"
                  />
                  <FormErrorMessage className="error-message">
                    {errors.email}
                  </FormErrorMessage>
                </FormControl>

                <FormControl 
                  isInvalid={errors.password && touched.password}
                  className="form-field password-field"
                >
                  <FormLabel>Password</FormLabel>
                  <Field
                    as={Input}
                    name="password"
                    type="password"
                    placeholder="Enter your password"
                  />
                  <FormErrorMessage className="error-message">
                    {errors.password}
                  </FormErrorMessage>
                </FormControl>

                <FormControl 
                  isInvalid={errors.confirmPassword && touched.confirmPassword}
                  className="form-field password-field"
                >
                  <FormLabel>Confirm Password</FormLabel>
                  <Field
                    as={Input}
                    name="confirmPassword"
                    type="password"
                    placeholder="Confirm your password"
                  />
                  <FormErrorMessage className="error-message">
                    {errors.confirmPassword}
                  </FormErrorMessage>
                </FormControl>

                <Button
                  type="submit"
                  colorScheme="blue"
                  className="submit-button"
                  {...styles.submitButton}
                >
                  Register
                </Button>
              </VStack>
            </Form>
          )}
        </Formik>
        <Text {...styles.linkText} className="register-link">
          Already have an account?{' '}
          <Link as={RouterLink} to="/login" color="blue.500">
            Login here
          </Link>
        </Text>
      </VStack>
    </Box>
  )
}

export default Register


