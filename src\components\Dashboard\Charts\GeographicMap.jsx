import { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  useColorModeValue,
  Button,
  Icon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Progress,
  Select,
  Divider
} from '@chakra-ui/react';
import { FiMapPin, FiGlobe, FiRefreshCw, FiDownload } from 'react-icons/fi';

// Sample data for regions
const regionData = [
  { 
    id: 1, 
    region: 'North America', 
    users: 12450, 
    percentage: 35.2, 
    growth: 8.3,
    countries: [
      { name: 'United States', users: 8920, percentage: 71.6 },
      { name: 'Canada', users: 2340, percentage: 18.8 },
      { name: 'Mexico', users: 1190, percentage: 9.6 }
    ]
  },
  { 
    id: 2, 
    region: 'Europe', 
    users: 10280, 
    percentage: 29.1, 
    growth: 5.7,
    countries: [
      { name: 'United Kingdom', users: 3150, percentage: 30.6 },
      { name: 'Germany', users: 2780, percentage: 27.0 },
      { name: 'France', users: 1920, percentage: 18.7 },
      { name: 'Spain', users: 1430, percentage: 13.9 },
      { name: 'Italy', users: 1000, percentage: 9.7 }
    ]
  },
  { 
    id: 3, 
    region: 'Asia Pacific', 
    users: 8640, 
    percentage: 24.4, 
    growth: 12.1,
    countries: [
      { name: 'China', users: 2840, percentage: 32.9 },
      { name: 'India', users: 2120, percentage: 24.5 },
      { name: 'Japan', users: 1580, percentage: 18.3 },
      { name: 'Australia', users: 1100, percentage: 12.7 },
      { name: 'South Korea', users: 1000, percentage: 11.6 }
    ]
  },
  { 
    id: 4, 
    region: 'South America', 
    users: 2560, 
    percentage: 7.2, 
    growth: 9.8,
    countries: [
      { name: 'Brazil', users: 1320, percentage: 51.6 },
      { name: 'Argentina', users: 580, percentage: 22.7 },
      { name: 'Colombia', users: 420, percentage: 16.4 },
      { name: 'Chile', users: 240, percentage: 9.4 }
    ]
  },
  { 
    id: 5, 
    region: 'Africa', 
    users: 1450, 
    percentage: 4.1, 
    growth: 15.2,
    countries: [
      { name: 'South Africa', users: 580, percentage: 40.0 },
      { name: 'Nigeria', users: 420, percentage: 29.0 },
      { name: 'Egypt', users: 280, percentage: 19.3 },
      { name: 'Kenya', users: 170, percentage: 11.7 }
    ]
  }
];

const GeographicMap = () => {
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [viewMode, setViewMode] = useState('regions');
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const headerBg = useColorModeValue('gray.50', 'gray.700');
  
  const totalUsers = regionData.reduce((sum, region) => sum + region.users, 0);
  
  const handleRegionClick = (region) => {
    setSelectedRegion(region);
    setViewMode('countries');
  };
  
  const handleBackToRegions = () => {
    setSelectedRegion(null);
    setViewMode('regions');
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">
            <HStack>
              <Icon as={FiGlobe} />
              <Text>Geographic Distribution</Text>
            </HStack>
          </Heading>
          <Text color={textColor} fontSize="sm">
            {viewMode === 'regions' 
              ? 'User distribution by region' 
              : `Countries in ${selectedRegion?.region}`}
          </Text>
        </Box>
        <HStack>
          {viewMode === 'countries' && (
            <Button size="sm" variant="ghost" onClick={handleBackToRegions}>
              Back to Regions
            </Button>
          )}
          <Select 
            size="sm" 
            width={{ base: '110px', md: '130px' }}
            defaultValue="users"
          >
            <option value="users">By Users</option>
            <option value="revenue">By Revenue</option>
            <option value="orders">By Orders</option>
          </Select>
          <Button size="sm" variant="ghost">
            <Icon as={FiDownload} />
          </Button>
        </HStack>
      </Flex>
      
      <Box overflowX="auto">
        <Table variant="simple" size="sm">
          <Thead bg={headerBg}>
            <Tr>
              <Th>{viewMode === 'regions' ? 'Region' : 'Country'}</Th>
              <Th isNumeric>Users</Th>
              <Th isNumeric>Percentage</Th>
              {viewMode === 'regions' && <Th isNumeric>Growth</Th>}
              <Th>Distribution</Th>
            </Tr>
          </Thead>
          <Tbody>
            {viewMode === 'regions' ? (
              regionData.map(region => (
                <Tr 
                  key={region.id}
                  _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                  cursor="pointer"
                  onClick={() => handleRegionClick(region)}
                >
                  <Td>
                    <HStack>
                      <Icon as={FiMapPin} color="blue.500" />
                      <Text fontWeight="medium">{region.region}</Text>
                    </HStack>
                  </Td>
                  <Td isNumeric>{region.users.toLocaleString()}</Td>
                  <Td isNumeric>{region.percentage}%</Td>
                  <Td isNumeric>
                    <Badge colorScheme={region.growth > 10 ? 'green' : region.growth > 5 ? 'blue' : 'gray'}>
                      +{region.growth}%
                    </Badge>
                  </Td>
                  <Td width="30%">
                    <Progress 
                      value={region.percentage} 
                      size="sm" 
                      colorScheme={
                        region.percentage > 30 ? 'blue' : 
                        region.percentage > 20 ? 'green' : 
                        region.percentage > 10 ? 'yellow' : 'orange'
                      }
                      borderRadius="full"
                    />
                  </Td>
                </Tr>
              ))
            ) : (
              selectedRegion?.countries.map((country, index) => (
                <Tr key={index}>
                  <Td>
                    <Text fontWeight="medium">{country.name}</Text>
                  </Td>
                  <Td isNumeric>{country.users.toLocaleString()}</Td>
                  <Td isNumeric>{country.percentage}%</Td>
                  <Td width="30%">
                    <Progress 
                      value={country.percentage} 
                      size="sm" 
                      colorScheme={
                        country.percentage > 50 ? 'blue' : 
                        country.percentage > 30 ? 'green' : 
                        country.percentage > 15 ? 'yellow' : 'orange'
                      }
                      borderRadius="full"
                    />
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </Box>
      
      <Divider my={4} />
      
      <Flex justify="space-between" align="center">
        <Text fontSize="xs" color={textColor}>
          <Icon as={FiRefreshCw} mr={1} />
          Last updated: {new Date().toLocaleDateString()}
        </Text>
        <Text fontSize="sm" fontWeight="medium">
          Total Users: {totalUsers.toLocaleString()}
        </Text>
      </Flex>
    </Box>
  );
};

export default GeographicMap;
