.splash-container {
  min-height: calc(100vh - 64px);
  animation: gradientBG 15s ease infinite;
}

.splash-heading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: slideDown 0.5s ease-out;
}

.splash-description {
  animation: fadeIn 0.5s ease-out 0.2s both;
}

.splash-button {
  transition: all 0.3s ease-in-out;
  animation: fadeIn 0.5s ease-out 0.4s both;
}

.splash-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.app-logo {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.app-logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
