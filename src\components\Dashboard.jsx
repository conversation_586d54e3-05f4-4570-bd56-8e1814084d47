import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Flex,
  Icon,
  Grid,
  GridItem,
  HStack,
  CircularProgress,
  CircularProgressLabel,
  Button
} from '@chakra-ui/react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend
} from 'recharts';
import {
  FiTrendingUp,
  FiUsers,
  FiDollarSign,
  FiShoppingCart,
  FiCpu,
  FiHardDrive,
  FiWifi,
  FiRefreshCw
} from 'react-icons/fi';

const Dashboard = () => {
  // Stats Cards Data - from otherfeatures.jsx
  const stats = [
    { title: 'Revenue', value: '$23,500', change: 23.36, icon: FiDollarSign, color: 'blue.50', iconColor: 'blue.500' },
    { title: 'New Users', value: '452', change: 12.05, icon: FiUsers, color: 'green.50', iconColor: 'green.500' },
    { title: 'Orders', value: '1,257', change: -3.05, icon: FiShoppingCart, color: 'purple.50', iconColor: 'purple.500' },
    { title: 'Growth', value: '18.4%', change: 5.14, icon: FiTrendingUp, color: 'orange.50', iconColor: 'orange.500' }
  ];

  // Performance Metrics - from PerformanceMonitor.jsx
  const [metrics, setMetrics] = useState({
    cpu: { value: 45, status: 'healthy', details: 'Normal usage' },
    memory: { value: 68, status: 'warning', details: 'High memory usage' },
    network: { value: 32, status: 'healthy', details: 'Network stable' },
    users: { value: 89, status: 'critical', details: 'Near capacity' }
  });

  // Chart Data - from SimpleChart.jsx
  const chartData = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: 300 },
    { name: 'Mar', value: 600 },
    { name: 'Apr', value: 800 },
    { name: 'May', value: 500 },
    { name: 'Jun', value: 900 },
    { name: 'Jul', value: 700 }
  ];

  // Helper function to get status color - from PerformanceMonitor.jsx
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'green.400';
      case 'warning': return 'orange.400';
      case 'critical': return 'red.400';
      default: return 'gray.400';
    }
  };

  // Refresh metric function - from PerformanceMonitor.jsx
  const refreshMetric = (key) => {
    setMetrics(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        value: Math.floor(Math.random() * 100)
      }
    }));
  };

  return (
    <Box py={6}>
      <Container maxW="container.xl">
        <Heading size="lg" mb={4}>Dashboard</Heading>
        <Text color="gray.600" mb={6}>Welcome to your dashboard overview</Text>

        {/* Stats Cards - from otherfeatures.jsx */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardBody>
                <Flex justify="space-between" align="center">
                  <Stat>
                    <StatLabel>{stat.title}</StatLabel>
                    <StatNumber>{stat.value}</StatNumber>
                    <StatHelpText>
                      <StatArrow type={stat.change >= 0 ? 'increase' : 'decrease'} />
                      {Math.abs(stat.change)}%
                    </StatHelpText>
                  </Stat>
                  <Box p={2} bg={stat.color} borderRadius="md">
                    <Icon as={stat.icon} boxSize={6} color={stat.iconColor} />
                  </Box>
                </Flex>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>

        {/* Chart Section - from SimpleChart.jsx */}
        <Card mb={8}>
          <CardBody>
            <Heading size="md" mb={4}>Sales Overview</Heading>
            <Box height="300px">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line type="monotone" dataKey="value" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>

        {/* Performance Metrics - from PerformanceMonitor.jsx */}
        <Heading size="md" mb={4}>System Performance</Heading>
        <Grid templateColumns={{ base: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={4} mb={8}>
          {Object.entries(metrics).map(([key, data]) => (
            <GridItem key={key}>
              <Card>
                <CardBody position="relative">
                  <HStack spacing={4} align="center">
                    <Icon
                      as={{
                        cpu: FiCpu,
                        memory: FiHardDrive,
                        network: FiWifi,
                        users: FiUsers
                      }[key]}
                      boxSize={6}
                      color={getStatusColor(data.status)}
                    />
                    <Box flex={1}>
                      <Text fontSize="sm" color="gray.500">{key.charAt(0).toUpperCase() + key.slice(1)} Usage</Text>
                      <Text fontSize="2xl" fontWeight="bold">{data.value}%</Text>
                    </Box>
                    <CircularProgress value={data.value} color={getStatusColor(data.status)} size="50px" thickness="8px">
                      <CircularProgressLabel>{data.value}%</CircularProgressLabel>
                    </CircularProgress>
                  </HStack>
                  {data.details && (
                    <Text fontSize="sm" color="gray.500" mt={2}>{data.details}</Text>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<Icon as={FiRefreshCw} />}
                    onClick={() => refreshMetric(key)}
                    position="absolute"
                    top={2}
                    right={2}
                    opacity={0.5}
                    _hover={{ opacity: 1 }}
                  >
                    Refresh
                  </Button>
                </CardBody>
              </Card>
            </GridItem>
          ))}
        </Grid>

        {/* Additional Dashboard Components */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          <Card>
            <CardBody>
              <Heading size="md" mb={4}>User Activity</Heading>
              <Text>Charts and graphs will be added here.</Text>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Heading size="md" mb={4}>Recent Orders</Heading>
              <Text>Recent order data will be displayed here.</Text>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Heading size="md" mb={4}>Performance</Heading>
              <Text>Performance metrics will be displayed here.</Text>
            </CardBody>
          </Card>
        </SimpleGrid>
      </Container>
    </Box>
  );
};

export default Dashboard;