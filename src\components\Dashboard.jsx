import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Flex,
  Icon,
  Grid,
  GridItem,
  HStack,
  VStack,
  CircularProgress,
  CircularProgressLabel,
  Button,
  Avatar,
  Badge,
  Divider,
  Select,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  AvatarGroup,
  useColorModeValue
} from '@chakra-ui/react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  BarChart,
  Bar
} from 'recharts';
import {
  FiTrendingUp,
  FiUsers,
  FiDollarSign,
  FiShoppingCart,
  FiCpu,
  FiHardDrive,
  FiWifi,
  FiRefreshCw,
  FiClock,
  FiArrowRight,
  FiPackage,
  FiCalendar,
  FiMoreVertical
} from 'react-icons/fi';

const Dashboard = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Analytics Summary Data - from AnalyticsSummary.jsx
  const [period, setPeriod] = useState('Last Month');
  const [analyticsStats, setAnalyticsStats] = useState([
    { title: 'Total Revenue', value: '$24,532', change: 12.5, icon: FiDollarSign },
    { title: 'New Customers', value: '321', change: 8.2, icon: FiUsers },
    { title: 'Total Orders', value: '1,463', change: -3.1, icon: FiShoppingCart },
    { title: 'Conversion Rate', value: '3.2%', change: 1.8, icon: FiTrendingUp }
  ]);

  // Performance Metrics - from PerformanceMonitor.jsx
  const [metrics, setMetrics] = useState({
    cpu: { value: 45, status: 'healthy', details: 'Normal usage' },
    memory: { value: 68, status: 'warning', details: 'High memory usage' },
    network: { value: 32, status: 'healthy', details: 'Network stable' },
    users: { value: 89, status: 'critical', details: 'Near capacity' }
  });

  // Recent Customers Data - from RecentCustomers.jsx
  const recentCustomers = [
    {
      id: 1,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      purchase: 'Premium Headphones',
      amount: 199.99,
      time: '10 minutes ago',
      status: 'completed',
      avatar: 'https://bit.ly/sage-adebayo',
    },
    {
      id: 2,
      name: 'Michael Chen',
      email: '<EMAIL>',
      purchase: 'Wireless Earbuds',
      amount: 149.99,
      time: '25 minutes ago',
      status: 'processing',
      avatar: 'https://bit.ly/ryan-florence',
    },
    {
      id: 3,
      name: 'Emma Wilson',
      email: '<EMAIL>',
      purchase: 'Smart Watch',
      amount: 299.99,
      time: '1 hour ago',
      status: 'completed',
      avatar: 'https://bit.ly/prosper-baba',
    },
  ];

  // Profit Loss Data - from ProfitLoss.jsx
  const profitLossData = [
    { month: 'Jan', profit: 4000, loss: -2400 },
    { month: 'Feb', profit: 3000, loss: -1398 },
    { month: 'Mar', profit: 2000, loss: -9800 },
    { month: 'Apr', profit: 2780, loss: -3908 },
    { month: 'May', profit: 1890, loss: -4800 },
    { month: 'Jun', profit: 2390, loss: -3800 },
  ];

  // Chart Data - from SimpleChart.jsx
  const chartData = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: 300 },
    { name: 'Mar', value: 600 },
    { name: 'Apr', value: 800 },
    { name: 'May', value: 500 },
    { name: 'Jun', value: 900 },
    { name: 'Jul', value: 700 }
  ];

  // Helper function to get status color - from PerformanceMonitor.jsx
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'green.400';
      case 'warning': return 'orange.400';
      case 'critical': return 'red.400';
      default: return 'gray.400';
    }
  };

  // Helper function for customer status color - from RecentCustomers.jsx
  const getCustomerStatusColor = (status) => {
    const colors = {
      completed: 'green',
      processing: 'orange',
      pending: 'yellow',
      failed: 'red',
    };
    return colors[status] || 'gray';
  };

  // Refresh metric function - from PerformanceMonitor.jsx
  const refreshMetric = (key) => {
    setMetrics(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        value: Math.floor(Math.random() * 100)
      }
    }));
  };

  // Update analytics stats based on period - from AnalyticsSummary.jsx
  useEffect(() => {
    const variations = {
      'Last Week': [
        { title: 'Total Revenue', value: '$5,832', change: 8.3, icon: FiDollarSign },
        { title: 'New Customers', value: '87', change: 12.1, icon: FiUsers },
        { title: 'Total Orders', value: '342', change: 5.7, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '3.5%', change: 2.2, icon: FiTrendingUp }
      ],
      'Last Month': [
        { title: 'Total Revenue', value: '$24,532', change: 12.5, icon: FiDollarSign },
        { title: 'New Customers', value: '321', change: 8.2, icon: FiUsers },
        { title: 'Total Orders', value: '1,463', change: -3.1, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '3.2%', change: 1.8, icon: FiTrendingUp }
      ],
      'Last Quarter': [
        { title: 'Total Revenue', value: '$78,215', change: 15.8, icon: FiDollarSign },
        { title: 'New Customers', value: '943', change: -2.4, icon: FiUsers },
        { title: 'Total Orders', value: '4,271', change: 7.3, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '2.9%', change: -0.5, icon: FiTrendingUp }
      ],
      'Last Year': [
        { title: 'Total Revenue', value: '$312,754', change: 22.7, icon: FiDollarSign },
        { title: 'New Customers', value: '3,842', change: 18.9, icon: FiUsers },
        { title: 'Total Orders', value: '17,583', change: 14.2, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '3.1%', change: 0.7, icon: FiTrendingUp }
      ]
    };

    setAnalyticsStats(variations[period] || variations['Last Month']);
  }, [period]);

  return (
    <Box py={6} bg={bgColor} minH="100vh">
      <Container maxW="container.xl">
        <Heading size="lg" mb={4}>Dashboard</Heading>
        <Text color="gray.600" mb={6}>Welcome to your comprehensive dashboard overview</Text>

        {/* Analytics Summary - from AnalyticsSummary.jsx */}
        <Card mb={8} bg={cardBgColor}>
          <CardBody>
            <Flex justify="space-between" align="center" mb={4}>
              <Heading size="md">Analytics Summary</Heading>
              <HStack>
                <Select
                  size="sm"
                  value={period}
                  onChange={(e) => setPeriod(e.target.value)}
                  width="150px"
                >
                  <option value="Last Week">Last Week</option>
                  <option value="Last Month">Last Month</option>
                  <option value="Last Quarter">Last Quarter</option>
                  <option value="Last Year">Last Year</option>
                </Select>
                <Menu>
                  <MenuButton as={Button} size="sm" variant="ghost">
                    <Icon as={FiMoreVertical} />
                  </MenuButton>
                  <MenuList>
                    <MenuItem>Download Report</MenuItem>
                    <MenuItem>Share Analytics</MenuItem>
                    <MenuItem>View Detailed Report</MenuItem>
                  </MenuList>
                </Menu>
              </HStack>
            </Flex>

            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mb={4}>
              {analyticsStats.map((stat, index) => (
                <Box
                  key={index}
                  bg={cardBgColor}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  transition="transform 0.3s"
                  _hover={{ transform: 'translateY(-4px)', boxShadow: 'md' }}
                >
                  <Flex justify="space-between" align="flex-start">
                    <Box>
                      <StatLabel color="gray.500" fontSize="sm">{stat.title}</StatLabel>
                      <StatNumber fontSize="2xl" fontWeight="bold" mt={1}>{stat.value}</StatNumber>
                      <StatHelpText mb={0}>
                        <StatArrow type={stat.change >= 0 ? 'increase' : 'decrease'} />
                        {Math.abs(stat.change)}% vs {period}
                      </StatHelpText>
                    </Box>
                    <Icon as={stat.icon} boxSize={6} color={stat.change >= 0 ? 'green.500' : 'red.500'} />
                  </Flex>
                </Box>
              ))}
            </SimpleGrid>

            <Divider my={6} />

            <Flex justify="space-between" align="center">
              <Text color="gray.500" fontSize="sm">
                <Icon as={FiCalendar} mr={1} />
                Last updated: {new Date().toLocaleDateString()}
              </Text>
              <Button size="sm" leftIcon={<Icon as={FiTrendingUp} />} colorScheme="blue" variant="outline">
                View Full Analytics
              </Button>
            </Flex>
          </CardBody>
        </Card>

        {/* Chart Section - from SimpleChart.jsx */}
        <Card mb={8}>
          <CardBody>
            <Heading size="md" mb={4}>Sales Overview</Heading>
            <Box height="300px">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line type="monotone" dataKey="value" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>

        {/* Performance Metrics - from PerformanceMonitor.jsx */}
        <Heading size="md" mb={4}>System Performance</Heading>
        <Grid templateColumns={{ base: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={4} mb={8}>
          {Object.entries(metrics).map(([key, data]) => (
            <GridItem key={key}>
              <Card>
                <CardBody position="relative">
                  <HStack spacing={4} align="center">
                    <Icon
                      as={{
                        cpu: FiCpu,
                        memory: FiHardDrive,
                        network: FiWifi,
                        users: FiUsers
                      }[key]}
                      boxSize={6}
                      color={getStatusColor(data.status)}
                    />
                    <Box flex={1}>
                      <Text fontSize="sm" color="gray.500">{key.charAt(0).toUpperCase() + key.slice(1)} Usage</Text>
                      <Text fontSize="2xl" fontWeight="bold">{data.value}%</Text>
                    </Box>
                    <CircularProgress value={data.value} color={getStatusColor(data.status)} size="50px" thickness="8px">
                      <CircularProgressLabel>{data.value}%</CircularProgressLabel>
                    </CircularProgress>
                  </HStack>
                  {data.details && (
                    <Text fontSize="sm" color="gray.500" mt={2}>{data.details}</Text>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<Icon as={FiRefreshCw} />}
                    onClick={() => refreshMetric(key)}
                    position="absolute"
                    top={2}
                    right={2}
                    opacity={0.5}
                    _hover={{ opacity: 1 }}
                  >
                    Refresh
                  </Button>
                </CardBody>
              </Card>
            </GridItem>
          ))}
        </Grid>

        {/* Additional Dashboard Components */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          <Card>
            <CardBody>
              <Heading size="md" mb={4}>User Activity</Heading>
              <Text>Charts and graphs will be added here.</Text>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Heading size="md" mb={4}>Recent Orders</Heading>
              <Text>Recent order data will be displayed here.</Text>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Heading size="md" mb={4}>Performance</Heading>
              <Text>Performance metrics will be displayed here.</Text>
            </CardBody>
          </Card>
        </SimpleGrid>
      </Container>
    </Box>
  );
};

export default Dashboard;