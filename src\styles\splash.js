export const splashStyles = {
  mainBox: {
    minH: "calc(100vh - 64px)",
    w: "100%",
    py: { base: "10", md: "20" },
    px: { base: "4", md: "8" },
    position: "relative",
    overflow: "hidden"
  },
  
  container: {
    maxW: "container.xl",
    position: "relative",
    zIndex: 1
  },
  
  content: {
    spacing: { base: "6", md: "8" },
    textAlign: "center",
    maxW: "800px",
    mx: "auto"
  },
  
  heading: {
    fontSize: { base: "4xl", md: "5xl", lg: "6xl" },
    fontWeight: "bold",
    bgGradient: "linear(to-r, blue.400, purple.500)",
    bgClip: "text",
    letterSpacing: "tight",
    lineHeight: "shorter",
    mb: { base: "4", md: "6" }
  },
  
  description: {
    fontSize: { base: "lg", md: "xl" },
    color: "gray.500",
    _dark: { color: "gray.400" },
    maxW: "600px",
    mx: "auto",
    mb: { base: "8", md: "10" }
  },
  
  buttonBox: {
    display: "flex",
    flexDirection: { base: "column", sm: "row" },
    gap: { base: "4", sm: "4" },
    justifyContent: "center",
    alignItems: "center",
    w: "100%",
    mt: { base: "6", md: "8" }
  }
}