import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FiRefreshCcw, FiHome } from 'react-icons/fi';

const ErrorPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const bgColor = useColorModeValue('gray.50', 'gray.800');
  const textColor = useColorModeValue('gray.600', 'gray.200');

  const error = location.state?.error || {
    message: 'An unexpected error occurred',
    status: 500
  };

  return (
    <Container maxW="container.xl" py={20}>
      <Box
        bg={bgColor}
        p={8}
        borderRadius="lg"
        textAlign="center"
      >
        <VStack spacing={6}>
          <Heading size="2xl">Oops!</Heading>
          <Heading size="xl">Something went wrong</Heading>
          <Text color={textColor} fontSize="lg">
            {error.message}
          </Text>
          {error.status && (
            <Text color={textColor} fontSize="md">
              Error Code: {error.status}
            </Text>
          )}
          <Box pt={6}>
            <VStack spacing={4}>
              <Button
                leftIcon={<FiRefreshCcw />}
                colorScheme="blue"
                size="lg"
                onClick={() => window.location.reload()}
                width="200px"
              >
                Refresh Page
              </Button>
              <Button
                leftIcon={<FiHome />}
                variant="outline"
                size="lg"
                onClick={() => navigate('/home')}
                width="200px"
              >
                Go Home
              </Button>
            </VStack>
          </Box>
        </VStack>
      </Box>
    </Container>
  );
};

export default ErrorPage;