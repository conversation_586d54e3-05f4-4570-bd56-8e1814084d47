.floating-button {
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: float 3s ease-in-out infinite;
  z-index: 1000;
}

.floating-button:hover {
  transform: scale(1.1) rotate(10deg);
  animation-play-state: paused;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.messages-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.1);
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.message-bubble {
  transition: all 0.3s ease;
}

.message-bubble:hover {
  transform: translateY(-2px);
}

.message-bubble:hover .message-actions {
  opacity: 1;
}

.user-message {
  margin-left: auto;
}

.ai-message {
  margin-right: auto;
}

.thinking-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.mode-button {
  transition: all 0.2s ease;
}

.mode-button:hover {
  transform: translateY(-1px);
}

.suggestion-button {
  transition: all 0.2s ease;
  white-space: nowrap;
}

.suggestion-button:hover {
  transform: scale(1.05);
}

/* Markdown Styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content pre {
  border-radius: 8px;
  margin: 1em 0;
}

.markdown-content code {
  font-family: 'Fira Code', monospace;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
}

/* Animation Keyframes */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .message-bubble {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .floating-button {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 90%;
  }

  .suggestion-button {
    font-size: 0.8em;
  }
}

/* Export Feature Styling */
.export-button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.export-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.export-button:active::after {
  width: 200%;
  height: 200%;
}

/* Message Timing Indicators */
.message-timestamp {
  font-size: 0.75rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.message-bubble:hover .message-timestamp {
  opacity: 1;
}

/* Delete Confirmation Animation */
.delete-dialog {
  animation: scaleIn 0.3s ease-out;
}

.delete-button {
  transition: all 0.2s ease;
}

.delete-button:hover {
  background-color: rgba(229, 62, 62, 0.9);
}

/* Message Distribution Chart */
.distribution-chart {
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.distribution-chart:hover {
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
}

/* Enhanced Animations */
@keyframes slideFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Professional Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark Mode Refinements */
@media (prefers-color-scheme: dark) {
  .analytics-panel {
    background: rgba(26, 32, 44, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .distribution-chart {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  }

  .topic-badge {
    background: rgba(66, 153, 225, 0.15);
  }

  .delete-button:hover {
    background-color: rgba(229, 62, 62, 0.8);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .analytics-panel {
    padding: 1rem;
  }

  .distribution-chart {
    height: 200px;
  }

  .topic-badge {
    font-size: 0.75rem;
  }
}

/* Chat Analytics Section */
.analytics-container {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.analytics-metric {
  padding: 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.08);
  margin: 0.5rem 0;
  transition: transform 0.2s ease;
}

.analytics-metric:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.12);
}

/* Message Distribution Chart */
.chart-container {
  height: 200px;
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Topic Analysis */
.topic-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
}

.topic-tag {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: rgba(66, 153, 225, 0.15);
  color: #63B3ED;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.topic-tag:hover {
  background: rgba(66, 153, 225, 0.25);
  transform: scale(1.05);
}

/* Export Features */
.export-section {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
}

.export-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  background: #4299E1;
  color: white;
  transition: all 0.2s ease;
}

.export-button:hover {
  background: #3182CE;
  transform: translateY(-2px);
}

/* Message Timing */
.message-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.25rem;
}

.timestamp {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Delete Confirmation */
.delete-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(26, 32, 44, 0.95);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.delete-dialog-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.delete-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  background: #E53E3E;
  color: white;
  transition: all 0.2s ease;
}

.delete-button:hover {
  background: #C53030;
}

/* Loading States */
.loading-indicator {
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #4299E1 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Interactive Elements */
.action-button {
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* Message Interactions */
.message-container {
  position: relative;
  padding: 1rem;
  margin: 0.5rem 0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.message-container:hover {
  background: rgba(255, 255, 255, 0.05);
}

.message-actions {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-container:hover .message-actions {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-container {
    padding: 0.5rem;
  }

  .topic-cloud {
    gap: 0.25rem;
  }

  .topic-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
  }

  .export-button {
    width: 100%;
    justify-content: center;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .analytics-metric {
    background: rgba(26, 32, 44, 0.8);
  }

  .topic-tag {
    background: rgba(66, 153, 225, 0.2);
  }

  .export-button {
    background: #2B6CB0;
  }

  .export-button:hover {
    background: #2C5282;
  }
}

/* AI Avatar animations */
.ai-avatar-container {
  transform-origin: center;
  animation: avatar-bounce 0.5s ease;
}

.ai-avatar {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.thinking-dots span {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: white;
  margin: 0 2px;
  animation: thinking-dots 1.4s infinite;
}

.thinking-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background-color: #48BB78;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.message-bubble {
  position: relative;
  transition: all 0.3s ease;
}

.message-bubble::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.user-message::after {
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent #3182CE;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
}

.ai-message::after {
  border-width: 8px 8px 8px 0;
  border-color: transparent #EDF2F7 transparent transparent;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
}

@keyframes thinking-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(72, 187, 120, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
  }
}

@keyframes avatar-bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Message container scroll styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}


