import React from 'react';
import {
  Container,
  Box,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Badge,
  Button,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiSearch, FiDownload, FiFilter } from 'react-icons/fi';

const TransactionsReview = () => {
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  const transactions = [
    {
      id: 'TRX-001',
      date: '2024-01-20',
      amount: '$1,234.56',
      status: 'completed',
      method: 'Credit Card',
      customer: '<PERSON>'
    },
    {
      id: 'TRX-002',
      date: '2024-01-19',
      amount: '$890.00',
      status: 'pending',
      method: 'PayPal',
      customer: '<PERSON>'
    },
    {
      id: 'TRX-003',
      date: '2024-01-19',
      amount: '$2,500.00',
      status: 'failed',
      method: 'Bank Transfer',
      customer: '<PERSON>'
    },
    // Add more transaction data as needed
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'green';
      case 'pending': return 'yellow';
      case 'failed': return 'red';
      default: return 'gray';
    }
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <HStack justify="space-between">
          <Box>
            <Text fontSize="2xl" fontWeight="bold">Transactions Review</Text>
            <Text color="gray.500">Review and manage all payment transactions</Text>
          </Box>
          <HStack spacing={4}>
            <Button leftIcon={<FiDownload />} variant="outline">
              Export
            </Button>
            <Button leftIcon={<FiFilter />} variant="outline">
              Filter
            </Button>
          </HStack>
        </HStack>

        <Box>
          <HStack spacing={4} mb={6}>
            <InputGroup>
              <InputLeftElement pointerEvents="none">
                <FiSearch color="gray.300" />
              </InputLeftElement>
              <Input placeholder="Search transactions..." />
            </InputGroup>
            <Select placeholder="Status" w="200px">
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
            </Select>
            <Select placeholder="Payment Method" w="200px">
              <option value="all">All Methods</option>
              <option value="credit-card">Credit Card</option>
              <option value="paypal">PayPal</option>
              <option value="bank-transfer">Bank Transfer</option>
            </Select>
          </HStack>

          <Box overflowX="auto">
            <Table variant="simple" borderWidth="1px" borderColor={borderColor}>
              <Thead bg={useColorModeValue('gray.50', 'gray.800')}>
                <Tr>
                  <Th>Transaction ID</Th>
                  <Th>Date</Th>
                  <Th>Amount</Th>
                  <Th>Status</Th>
                  <Th>Payment Method</Th>
                  <Th>Customer</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {transactions.map((transaction) => (
                  <Tr key={transaction.id}>
                    <Td>{transaction.id}</Td>
                    <Td>{transaction.date}</Td>
                    <Td>{transaction.amount}</Td>
                    <Td>
                      <Badge colorScheme={getStatusColor(transaction.status)}>
                        {transaction.status}
                      </Badge>
                    </Td>
                    <Td>{transaction.method}</Td>
                    <Td>{transaction.customer}</Td>
                    <Td>
                      <Button size="sm" variant="ghost">
                        View Details
                      </Button>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        </Box>
      </VStack>
    </Container>
  );
};

export default TransactionsReview;