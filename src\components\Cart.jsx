import React from 'react';
import {
  Container,
  VStack,
  Heading,
  Button,
  Text,
  Box,
  HStack,
  Image,
  IconButton,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  useColorModeValue,
  Badge,
} from '@chakra-ui/react';
import { FiTrash2 } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../context/CartContext';

const Cart = () => {
  const navigate = useNavigate();
  const { cart, removeFromCart, updateQuantity, getCartTotal } = useCart();
  const cardBg = useColorModeValue('white', 'gray.700');

  if (cart.length === 0) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={4} align="center">
          <Heading size="lg">Your cart is empty</Heading>
          <Button
            colorScheme="blue"
            onClick={() => navigate('/products')}
          >
            Continue Shopping
          </Button>
        </VStack>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      <Heading size="lg" mb={6}>Shopping Cart</Heading>
      <VStack spacing={4} align="stretch">
        {cart.map((item) => (
          <Box 
            key={`${item.id}-${item.variant?.id || 'no-variant'}`} 
            p={4} 
            borderWidth="1px" 
            borderRadius="lg" 
            bg={cardBg}
          >
            <HStack spacing={4}>
              <Image
                src={item.image}
                alt={item.name}
                boxSize="100px"
                objectFit="cover"
                borderRadius="md"
              />
              <VStack flex={1} align="start" spacing={2}>
                <Heading size="md">{item.name}</Heading>
                {item.variant && (
                  <Badge colorScheme="purple">
                    {item.variant.name}
                  </Badge>
                )}
                <Text color="blue.500" fontSize="lg">
                  ${item.price.toFixed(2)}
                </Text>
              </VStack>
              <NumberInput
                maxW="100px"
                min={1}
                value={item.quantity}
                onChange={(_, value) => updateQuantity(item.id, item.variant?.id, value)}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
              <IconButton
                icon={<FiTrash2 />}
                onClick={() => removeFromCart(item.id, item.variant?.id)}
                variant="ghost"
                colorScheme="red"
              />
            </HStack>
          </Box>
        ))}
        
        <Box p={4} borderWidth="1px" borderRadius="lg" bg={cardBg}>
          <HStack justify="space-between">
            <Heading size="md">Total</Heading>
            <Heading size="md">${getCartTotal().toFixed(2)}</Heading>
          </HStack>
        </Box>
        
        <HStack spacing={4} justify="flex-end">
          <Button variant="outline" onClick={() => navigate('/products')}>
            Continue Shopping
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={() => navigate('/checkout')}
          >
            Proceed to Checkout
          </Button>
        </HStack>
      </VStack>
    </Container>
  );
};

export default Cart;








