Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF60A20000 ntdll.dll
7FFF5F700000 KERNEL32.DLL
7FFF5E3D0000 KERNELBASE.dll
7FFF60610000 USER32.dll
7FFF5E110000 win32u.dll
7FFF604C0000 GDI32.dll
7FFF5DCE0000 gdi32full.dll
7FFF5E140000 msvcp_win.dll
7FFF5E280000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF5F640000 advapi32.dll
7FFF60560000 msvcrt.dll
7FFF5F860000 sechost.dll
7FFF5E840000 RPCRT4.dll
7FFF5D130000 CRYPTBASE.DLL
7FFF5E7A0000 bcryptPrimitives.dll
7FFF5F1E0000 IMM32.DLL
