import { Component } from 'react';
import { Box, Heading, Text, Button } from '@chakra-ui/react';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box p={4} textAlign="center">
          <Heading size="lg" mb={4}>Something went wrong</Heading>
          <Text mb={4}>We're sorry - something's gone wrong on our end.</Text>
          <Button
            colorScheme="blue"
            onClick={() => window.location.reload()}
            mr={2}
          >
            Refresh Page
          </Button>
          <Button
            variant="outline"
            onClick={() => window.location.href = '/home'}
          >
            Go Home
          </Button>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
