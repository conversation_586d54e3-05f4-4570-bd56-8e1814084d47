{"name": "ecommerce-backend", "version": "1.0.0", "description": "E-commerce backend with MongoDB, authentication, and payment integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "setup": "node setup.js", "seed": "node seeders/index.js seed", "seed:clear": "node seeders/index.js clear", "db:reset": "npm run seed:clear && npm run seed"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "crypto": "^1.0.1", "axios": "^1.6.2", "stripe": "^14.7.0", "paypal-rest-sdk": "^1.8.1", "mpesa-node": "^1.0.0", "socket.io": "^4.7.4", "redis": "^4.6.10", "winston": "^3.11.0", "compression": "^1.7.4", "express-mongo-sanitize": "^2.2.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["ecommerce", "mongodb", "authentication", "payments", "mpesa", "paypal"], "author": "Your Name", "license": "MIT"}