import {
  Container,
  SimpleGrid,
  Box,
  Image,
  Heading,
  Text,
  HStack,
  Icon,
  VStack,
  Select,
  Input,
  Skeleton,
  useToast,
  Badge,
} from '@chakra-ui/react'
import { useState, useEffect } from 'react'
import { useNavigate, Link as RouterLink } from 'react-router-dom'
import { FiStar } from 'react-icons/fi'
import { productService } from '../services/productService'

const Products = () => {
  const [products, setProducts] = useState([
    {
      id: '1',
      name: 'Premium Headphones',
      price: 299.99,
      rating: 4.5,
      sales: 1200,
      description: 'High-quality wireless headphones with noise cancellation',
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
      category: 'electronics'
    },
    {
      id: '2',
      name: 'Smart Watch',
      price: 199.99,
      rating: 4.3,
      sales: 800,
      description: 'Feature-rich smartwatch with health tracking',
      image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500',
      category: 'electronics'
    },
    {
      id: '13',
      name: 'Gaming Mouse',
      price: 79.99,
      rating: 4.7,
      sales: 2200,
      description: 'High-precision gaming mouse with RGB lighting',
      image: 'https://images.unsplash.com/photo-1527814050087-3793815479db?w=500',
      category: 'electronics'
    },
    {
      id: '14',
      name: 'Mechanical Keyboard',
      price: 149.99,
      rating: 4.8,
      sales: 1600,
      description: 'RGB mechanical keyboard with custom switches',
      image: 'https://images.unsplash.com/photo-1595225476474-87563907a212?w=500',
      category: 'electronics'
    },
    {
      id: '15',
      name: 'Winter Coat',
      price: 199.99,
      rating: 4.6,
      sales: 890,
      description: 'Warm winter coat with water-resistant exterior',
      image: 'https://images.unsplash.com/photo-1539533113208-f6df8cc8b543?w=500',
      category: 'clothing'
    },
    {
      id: '16',
      name: 'Designer Watch',
      price: 299.99,
      rating: 4.9,
      sales: 750,
      description: 'Luxury analog watch with genuine leather strap',
      image: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=500',
      category: 'accessories'
    },
    {
      id: '17',
      name: 'Wireless Mouse',
      price: 49.99,
      rating: 4.4,
      sales: 3100,
      description: 'Ergonomic wireless mouse for productivity',
      image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500',
      category: 'electronics'
    },
    {
      id: '18',
      name: 'Summer Dress',
      price: 79.99,
      rating: 4.5,
      sales: 1200,
      description: 'Lightweight floral summer dress',
      image: 'https://images.unsplash.com/photo-1572804013309-59a88b7e92f1?w=500',
      category: 'clothing'
    },
    {
      id: '19',
      name: 'Laptop Stand',
      price: 39.99,
      rating: 4.3,
      sales: 2800,
      description: 'Adjustable aluminum laptop stand',
      image: 'https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=500',
      category: 'accessories'
    },
    {
      id: '20',
      name: 'Gaming Headset',
      price: 129.99,
      rating: 4.6,
      sales: 1900,
      description: '7.1 surround sound gaming headset',
      image: 'https://images.unsplash.com/photo-1599669454699-248893623440?w=500',
      category: 'electronics'
    },
    {
      id: '21',
      name: 'Leather Belt',
      price: 45.99,
      rating: 4.4,
      sales: 2100,
      description: 'Genuine leather belt with classic buckle',
      image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500',
      category: 'accessories'
    },
    {
      id: '22',
      name: 'Wireless Charger',
      price: 34.99,
      rating: 4.3,
      sales: 3400,
      description: 'Fast wireless charging pad for smartphones',
      image: 'https://images.unsplash.com/photo-1586816879360-004f5b0c51e3?w=500',
      category: 'electronics'
    },
    {
      id: '23',
      name: 'Sports Shorts',
      price: 29.99,
      rating: 4.4,
      sales: 2600,
      description: 'Breathable sports shorts with pockets',
      image: 'https://images.unsplash.com/photo-1591195853828-11db59a44f6b?w=500',
      category: 'clothing'
    },
    {
      id: '24',
      name: 'Tablet Stand',
      price: 25.99,
      rating: 4.2,
      sales: 1800,
      description: 'Adjustable tablet stand with non-slip base',
      image: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=500',
      category: 'accessories'
    },
    {
      id: '25',
      name: 'Bluetooth Speaker',
      price: 89.99,
      rating: 4.5,
      sales: 2900,
      description: 'Portable waterproof bluetooth speaker',
      image: 'https://images.unsplash.com/photo-1589003077984-894e133dabab?w=500',
      category: 'electronics'
    },
    {
      id: '26',
      name: 'Casual Sneakers',
      price: 69.99,
      rating: 4.4,
      sales: 2200,
      description: 'Comfortable casual sneakers for everyday wear',
      image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500',
      category: 'clothing'
    },
    {
      id: '27',
      name: 'Camera Bag',
      price: 59.99,
      rating: 4.3,
      sales: 1500,
      description: 'Padded camera bag with multiple compartments',
      image: 'https://images.unsplash.com/photo-1547949003-9792a18a2601?w=500',
      category: 'accessories'
    },
    {
      id: '28',
      name: 'Desk Lamp',
      price: 49.99,
      rating: 4.5,
      sales: 2100,
      description: 'LED desk lamp with adjustable brightness',
      image: 'https://images.unsplash.com/photo-1534073828943-f801091bb18f?w=500',
      category: 'electronics'
    },
    {
      id: '29',
      name: 'Wool Scarf',
      price: 35.99,
      rating: 4.6,
      sales: 1700,
      description: 'Soft wool scarf for winter',
      image: 'https://images.unsplash.com/photo-1584613862210-c663f471f3b3?w=500',
      category: 'accessories'
    },
    {
      id: '30',
      name: 'USB-C Hub',
      price: 45.99,
      rating: 4.4,
      sales: 2800,
      description: '7-in-1 USB-C hub with HDMI output',
      image: 'https://images.unsplash.com/photo-1616586169190-988d1e2f2587?w=500',
      category: 'electronics'
    }
  ]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [category, setCategory] = useState('all');
  const toast = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      filterProducts();
    }
  }, [searchQuery, sortBy, category, products]);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const data = await productService.getAllProducts();
      if (data && data.length > 0) {
        setProducts(data);
        setFilteredProducts(data);
      } else {
        // If API returns empty, use sample data
        setFilteredProducts(products);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load products. Showing sample data.',
        status: 'error',
        duration: 5000,
      });
      // Use sample data as fallback
      setFilteredProducts(products);
    } finally {
      setIsLoading(false);
    }
  };

  const filterProducts = () => {
    let filtered = [...products];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (category !== 'all') {
      filtered = filtered.filter(product => product.category === category);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredProducts(filtered);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <Heading size="lg" mb={4}>Our Products</Heading>

        <HStack spacing={4} mb={6}>
          <Input
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            w="300px"
          />
          <Select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            w="200px"
          >
            <option value="all">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="clothing">Clothing</option>
            <option value="accessories">Accessories</option>
          </Select>
          <Select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            w="200px"
          >
            <option value="name">Name</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Rating</option>
          </Select>
        </HStack>

        <SimpleGrid columns={{ base: 1, md: 3, lg: 4 }} spacing={6}>
          {isLoading
            ? Array(8).fill(0).map((_, i) => (
                <Skeleton key={i} height="300px" />
              ))
            : filteredProducts.map(product => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onClick={() => navigate(`/product/${product.id}`)}
                />
              ))}
        </SimpleGrid>
      </VStack>
    </Container>
  );
};

const ProductCard = ({ product }) => (
  <Box
    as={RouterLink}
    to={`/product/${product.id}`}
    borderWidth={1}
    borderRadius="lg"
    overflow="hidden"
    cursor="pointer"
    transition="transform 0.2s"
    _hover={{ transform: 'scale(1.02)' }}
    bg="white"
    shadow="md"
  >
    <Image
      src={product.image}
      alt={product.name}
      height="200px"
      width="100%"
      objectFit="cover"
    />
    <Box p={4}>
      <Heading size="md" mb={2}>{product.name}</Heading>
      <Text fontSize="xl" fontWeight="bold" color="blue.500" mb={2}>
        ${product.price}
      </Text>
      <HStack spacing={2} mb={2}>
        <Icon as={FiStar} color="yellow.400" />
        <Text>{product.rating}</Text>
        <Text color="gray.500">({product.sales} sold)</Text>
      </HStack>
      {product.variants && (
        <Badge colorScheme="purple" mb={2}>
          {product.variants.length} variants available
        </Badge>
      )}
      <Text noOfLines={2} color="gray.600">
        {product.description}
      </Text>
    </Box>
  </Box>
);

export default Products;












