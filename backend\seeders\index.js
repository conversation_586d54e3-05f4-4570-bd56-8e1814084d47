import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/User.js';
import Category from '../models/Category.js';
import Product from '../models/Product.js';
import { logger } from '../utils/logger.js';

dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('MongoDB connected for seeding');
  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
};

// Sample data
const sampleUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    isEmailVerified: true,
    phone: '+254700000000'
  },
  {
    username: 'johndo<PERSON>',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: '<PERSON>',
    lastName: 'Doe',
    role: 'user',
    isEmailVerified: true,
    phone: '+254700000001'
  },
  {
    username: 'jane<PERSON>',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'user',
    isEmailVerified: true,
    phone: '+254700000002'
  }
];

const sampleCategories = [
  {
    name: 'Electronics',
    description: 'Electronic devices and gadgets',
    icon: 'FiSmartphone',
    color: '#007bff',
    featured: true,
    sortOrder: 1
  },
  {
    name: 'Accessories',
    description: 'Fashion and lifestyle accessories',
    icon: 'FiWatch',
    color: '#28a745',
    featured: true,
    sortOrder: 2
  },
  {
    name: 'Home & Garden',
    description: 'Home improvement and garden supplies',
    icon: 'FiHome',
    color: '#ffc107',
    featured: false,
    sortOrder: 3
  },
  {
    name: 'Sports & Fitness',
    description: 'Sports equipment and fitness gear',
    icon: 'FiActivity',
    color: '#dc3545',
    featured: false,
    sortOrder: 4
  }
];

const sampleProducts = [
  {
    name: 'Premium Wireless Headphones',
    description: 'High-quality wireless headphones with active noise cancellation and premium sound quality.',
    shortDescription: 'Premium wireless headphones with noise cancellation',
    price: 299.99,
    comparePrice: 399.99,
    cost: 150.00,
    sku: 'PWH-001',
    inventory: {
      quantity: 50,
      trackQuantity: true,
      lowStockThreshold: 10
    },
    images: [{
      url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
      alt: 'Premium Wireless Headphones',
      isPrimary: true
    }],
    specifications: [
      { name: 'Battery Life', value: '30 hours' },
      { name: 'Wireless Range', value: '10 meters' },
      { name: 'Driver Size', value: '40mm' },
      { name: 'Frequency Response', value: '20Hz - 20kHz' }
    ],
    features: ['Active Noise Cancellation', 'Wireless Charging', 'Voice Assistant Support'],
    tags: ['wireless', 'headphones', 'premium', 'noise-cancellation'],
    status: 'active',
    featured: true
  },
  {
    name: 'Smart Fitness Watch',
    description: 'Advanced fitness tracking watch with heart rate monitoring, GPS, and smartphone connectivity.',
    shortDescription: 'Smart fitness watch with heart rate monitoring',
    price: 199.99,
    comparePrice: 249.99,
    cost: 100.00,
    sku: 'SFW-002',
    inventory: {
      quantity: 75,
      trackQuantity: true,
      lowStockThreshold: 15
    },
    images: [{
      url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500',
      alt: 'Smart Fitness Watch',
      isPrimary: true
    }],
    specifications: [
      { name: 'Battery Life', value: '7 days' },
      { name: 'Water Resistance', value: '50m' },
      { name: 'Display', value: '1.4" AMOLED' },
      { name: 'Sensors', value: 'Heart Rate, GPS, Accelerometer' }
    ],
    features: ['Heart Rate Monitoring', 'GPS Tracking', 'Sleep Analysis', 'Smartphone Notifications'],
    tags: ['smartwatch', 'fitness', 'health', 'gps'],
    status: 'active',
    featured: true
  },
  {
    name: 'Professional Camera Lens',
    description: 'High-quality camera lens for professional photography with superior optical performance.',
    shortDescription: 'Professional camera lens for photography',
    price: 899.99,
    cost: 450.00,
    sku: 'PCL-003',
    inventory: {
      quantity: 25,
      trackQuantity: true,
      lowStockThreshold: 5
    },
    images: [{
      url: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=500',
      alt: 'Professional Camera Lens',
      isPrimary: true
    }],
    specifications: [
      { name: 'Focal Length', value: '24-70mm' },
      { name: 'Aperture', value: 'f/2.8' },
      { name: 'Mount', value: 'Canon EF' },
      { name: 'Weight', value: '805g' }
    ],
    features: ['Weather Sealed', 'Image Stabilization', 'Ultra-Sonic Motor'],
    tags: ['camera', 'lens', 'photography', 'professional'],
    status: 'active',
    featured: false
  }
];

// Seed functions
const seedUsers = async () => {
  try {
    await User.deleteMany({});
    
    const users = [];
    for (const userData of sampleUsers) {
      const user = new User(userData);
      await user.save();
      users.push(user);
    }
    
    logger.info(`${users.length} users seeded successfully`);
    return users;
  } catch (error) {
    logger.error('Error seeding users:', error);
    throw error;
  }
};

const seedCategories = async (adminUser) => {
  try {
    await Category.deleteMany({});
    
    const categories = [];
    for (const categoryData of sampleCategories) {
      const category = await Category.create({
        ...categoryData,
        createdBy: adminUser._id
      });
      categories.push(category);
    }
    
    logger.info(`${categories.length} categories seeded successfully`);
    return categories;
  } catch (error) {
    logger.error('Error seeding categories:', error);
    throw error;
  }
};

const seedProducts = async (adminUser, categories) => {
  try {
    await Product.deleteMany({});
    
    const products = [];
    for (let i = 0; i < sampleProducts.length; i++) {
      const productData = sampleProducts[i];
      const category = categories[i % categories.length]; // Distribute products across categories
      
      const product = await Product.create({
        ...productData,
        category: category._id,
        createdBy: adminUser._id
      });
      products.push(product);
    }
    
    logger.info(`${products.length} products seeded successfully`);
    return products;
  } catch (error) {
    logger.error('Error seeding products:', error);
    throw error;
  }
};

// Main seeder function
const seedDatabase = async () => {
  try {
    await connectDB();
    
    logger.info('Starting database seeding...');
    
    // Seed users first
    const users = await seedUsers();
    const adminUser = users.find(user => user.role === 'admin');
    
    // Seed categories
    const categories = await seedCategories(adminUser);
    
    // Seed products
    await seedProducts(adminUser, categories);
    
    logger.info('Database seeding completed successfully!');
    process.exit(0);
    
  } catch (error) {
    logger.error('Database seeding failed:', error);
    process.exit(1);
  }
};

// Clear database function
const clearDatabase = async () => {
  try {
    await connectDB();
    
    logger.info('Clearing database...');
    
    await Promise.all([
      User.deleteMany({}),
      Category.deleteMany({}),
      Product.deleteMany({})
    ]);
    
    logger.info('Database cleared successfully!');
    process.exit(0);
    
  } catch (error) {
    logger.error('Database clearing failed:', error);
    process.exit(1);
  }
};

// Run seeder based on command line argument
const command = process.argv[2];

switch (command) {
  case 'seed':
    seedDatabase();
    break;
  case 'clear':
    clearDatabase();
    break;
  default:
    logger.info('Available commands:');
    logger.info('  npm run seed - Seed the database with sample data');
    logger.info('  npm run seed:clear - Clear all data from the database');
    process.exit(0);
}

export { seedDatabase, clearDatabase };
