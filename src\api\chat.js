import express from 'express';
import { Configuration, OpenAIApi } from 'openai';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();

// Initialize OpenAI
const configuration = new Configuration({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
});
const openai = new OpenAIApi(configuration);

// Middleware to verify API key
const verifyApiKey = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'Missing or invalid API key' });
  }

  const apiKey = authHeader.split(' ')[1];
  
  // Verify against your stored API keys
  // eslint-disable-next-line no-undef
  if (apiKey !== process.env.VALID_API_KEY) {
    return res.status(401).json({ message: 'Invalid API key' });
  }

  next();
};

router.post('/chat', verifyApiKey, async (req, res) => {
  try {
    const { messages, model = 'gpt-3.5-turbo', temperature = 0.7, max_tokens = 150 } = req.body;

    // Validate request body
    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ 
        message: 'Invalid request body. Messages array is required.' 
      });
    }

    // Create completion with OpenAI
    const completion = await openai.createChatCompletion({
      model,
      messages,
      temperature,
      max_tokens,
    });

    // Format response to match client expectations
    const response = {
      choices: [{
        message: {
          content: completion.data.choices[0].message.content,
          role: 'assistant'
        }
      }]
    };

    res.json(response);

  } catch (error) {
    console.error('Chat API Error:', error);

    // Handle different types of errors
    if (error.response) {
      // OpenAI API error
      return res.status(error.response.status).json({
        message: error.response.data.error.message
      });
    } else if (error.request) {
      // Network error
      return res.status(503).json({
        message: 'Service temporarily unavailable'
      });
    } else {
      // Other errors
      return res.status(500).json({
        message: 'Internal server error'
      });
    }
  }
});

export default router;
