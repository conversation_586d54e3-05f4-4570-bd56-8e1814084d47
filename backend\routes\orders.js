import express from 'express';
import { body, validationResult } from 'express-validator';
import Order from '../models/Order.js';
import Product from '../models/Product.js';
import { protect, authorize } from '../middleware/auth.js';
import { sendEmail } from '../utils/email.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
router.post('/', protect, [
  body('items').isArray({ min: 1 }).withMessage('Order must contain at least one item'),
  body('items.*.product').isMongoId().withMessage('Valid product ID is required'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
  body('shippingAddress.firstName').notEmpty().withMessage('First name is required'),
  body('shippingAddress.lastName').notEmpty().withMessage('Last name is required'),
  body('shippingAddress.street').notEmpty().withMessage('Street address is required'),
  body('shippingAddress.city').notEmpty().withMessage('City is required'),
  body('shippingAddress.state').notEmpty().withMessage('State is required'),
  body('shippingAddress.zipCode').notEmpty().withMessage('Zip code is required'),
  body('shippingAddress.country').notEmpty().withMessage('Country is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { items, shippingAddress, billingAddress, shipping, coupon, notes } = req.body;

    // Validate products and calculate pricing
    const orderItems = [];
    let subtotal = 0;

    for (const item of items) {
      const product = await Product.findById(item.product);
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: `Product not found: ${item.product}`
        });
      }

      if (product.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: `Product is not available: ${product.name}`
        });
      }

      if (!product.isInStock(item.quantity)) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for product: ${product.name}`
        });
      }

      const orderItem = {
        product: product._id,
        name: product.name,
        image: product.primaryImage,
        price: product.price,
        quantity: item.quantity,
        sku: product.sku,
        attributes: item.attributes || {}
      };

      orderItems.push(orderItem);
      subtotal += product.price * item.quantity;

      // Reserve inventory
      product.reserveInventory(item.quantity);
      await product.save();
    }

    // Calculate pricing
    const tax = subtotal * 0.16; // 16% VAT for Kenya
    const shippingCost = shipping?.cost || 0;
    const discount = coupon?.discount || 0;
    const total = subtotal + tax + shippingCost - discount;

    // Create order
    const orderData = {
      user: req.user.id,
      items: orderItems,
      shippingAddress,
      billingAddress: billingAddress || shippingAddress,
      pricing: {
        subtotal,
        tax,
        shipping: shippingCost,
        discount,
        total
      },
      shipping: {
        method: shipping?.method || 'standard',
        cost: shippingCost,
        estimatedDelivery: shipping?.estimatedDelivery
      },
      coupon,
      notes,
      payment: {
        method: 'pending',
        status: 'pending',
        amount: total,
        currency: 'KES'
      }
    };

    const order = await Order.create(orderData);

    // Send order confirmation email
    try {
      await sendEmail({
        email: req.user.email,
        template: 'orderConfirmation',
        data: {
          customerName: `${req.user.firstName} ${req.user.lastName}`,
          orderNumber: order.orderNumber,
          orderId: order._id,
          orderDate: order.createdAt,
          totalAmount: order.pricing.total,
          currency: order.payment.currency,
          items: order.items,
          shippingAddress: order.shippingAddress
        }
      });
    } catch (emailError) {
      logger.error('Order confirmation email failed:', emailError);
    }

    // Emit real-time notification
    const io = req.app.get('io');
    io.emit('new_order', {
      orderId: order._id,
      orderNumber: order.orderNumber,
      customer: `${req.user.firstName} ${req.user.lastName}`,
      total: order.pricing.total
    });

    logger.info(`Order created: ${order.orderNumber} by ${req.user.email}`);

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: { order }
    });

  } catch (error) {
    logger.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get user orders
// @route   GET /api/orders/my-orders
// @access  Private
router.get('/my-orders', protect, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = { user: req.user.id };

    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }

    const orders = await Order.find(query)
      .populate('items.product', 'name images slug')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Get user orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('user', 'firstName lastName email')
      .populate('items.product', 'name images slug')
      .populate('timeline.updatedBy', 'firstName lastName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user owns the order or is admin
    if (order.user._id.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { order }
    });

  } catch (error) {
    logger.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update order status (Admin only)
// @route   PUT /api/orders/:id/status
// @access  Private/Admin
router.put('/:id/status', protect, authorize('admin'), [
  body('status').isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
    .withMessage('Invalid status'),
  body('note').optional().isLength({ max: 500 }).withMessage('Note cannot exceed 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, note } = req.body;
    const order = await Order.findById(req.params.id).populate('user', 'firstName lastName email');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update order status
    order.updateStatus(status, note, req.user.id);
    await order.save();

    // Send notification email based on status
    if (status === 'shipped' && order.shipping.tracking.trackingNumber) {
      try {
        await sendEmail({
          email: order.user.email,
          template: 'orderShipped',
          data: {
            customerName: `${order.user.firstName} ${order.user.lastName}`,
            orderNumber: order.orderNumber,
            trackingNumber: order.shipping.tracking.trackingNumber,
            carrier: order.shipping.tracking.carrier,
            estimatedDelivery: order.shipping.estimatedDelivery,
            trackingUrl: order.shipping.tracking.trackingUrl
          }
        });
      } catch (emailError) {
        logger.error('Shipping notification email failed:', emailError);
      }
    }

    // Emit real-time notification
    const io = req.app.get('io');
    io.to(`user_${order.user._id}`).emit('order_status_updated', {
      orderId: order._id,
      orderNumber: order.orderNumber,
      status: order.status,
      note
    });

    logger.info(`Order status updated: ${order.orderNumber} to ${status} by ${req.user.email}`);

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: { order }
    });

  } catch (error) {
    logger.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
router.put('/:id/cancel', protect, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user owns the order or is admin
    if (order.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if order can be cancelled
    if (!order.canBeCancelled()) {
      return res.status(400).json({
        success: false,
        message: 'Order cannot be cancelled at this stage'
      });
    }

    // Update order status
    order.updateStatus('cancelled', 'Order cancelled by customer', req.user.id);
    await order.save();

    // Restore inventory
    for (const item of order.items) {
      await Product.findByIdAndUpdate(item.product, {
        $inc: { 'inventory.quantity': item.quantity }
      });
    }

    logger.info(`Order cancelled: ${order.orderNumber} by ${req.user.email}`);

    res.json({
      success: true,
      message: 'Order cancelled successfully'
    });

  } catch (error) {
    logger.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
