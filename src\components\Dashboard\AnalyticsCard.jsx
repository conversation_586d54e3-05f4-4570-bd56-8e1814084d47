import {
  Box, Stat, StatLabel, StatNumber, StatHelpText,
  StatArrow, Icon, HStack, Tooltip, Progress,
  useColorModeValue
} from '@chakra-ui/react';
import { FiTrendingUp, FiTrendingDown, FiInfo } from 'react-icons/fi';

const AnalyticsCard = ({ title, value, change, description, icon, color = "blue" }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue(`${color}.50`, `${color}.800`);
  
  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="xl"
      borderLeft="4px"
      borderColor={borderColor}
      boxShadow="lg"
      transition="transform 0.2s"
      _hover={{ transform: 'translateY(-2px)' }}
    >
      <HStack justify="space-between" mb={2}>
        <Icon as={icon} boxSize={6} color={`${color}.500`} />
        <Tooltip label={description} hasArrow>
          <Icon as={FiInfo} cursor="pointer" opacity={0.5} />
        </Tooltip>
      </HStack>
      
      <Stat>
        <StatLabel fontSize="sm" color="gray.500">{title}</StatLabel>
        <StatNumber fontSize="2xl" fontWeight="bold">{value}</StatNumber>
        <StatHelpText>
          <StatArrow type={change >= 0 ? 'increase' : 'decrease'} />
          {Math.abs(change)}%
        </StatHelpText>
      </Stat>
      
      <Progress
        value={Math.abs(change)}
        colorScheme={color}
        size="xs"
        borderRadius="full"
        mt={2}
      />
    </Box>
  );
};

export default AnalyticsCard;