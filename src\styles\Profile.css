.profile-container {
  animation: fadeIn 0.5s ease-out;
}

.profile-avatar {
  transition: transform 0.3s ease-in-out;
}

.profile-avatar:hover {
  transform: scale(1.05);
}

.social-icon {
  transition: all 0.3s ease-in-out !important;
}

.social-icon:hover {
  transform: translateY(-2px) !important;
}

.achievement-card {
  transition: all 0.3s ease-in-out;
}

.achievement-card:hover {
  transform: translateX(5px);
  box-shadow: var(--chakra-shadows-lg);
}

.skill-bar {
  transition: width 1s ease-in-out;
}

.tab-panel {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .achievement-card:hover {
    transform: translateY(5px);
  }
}

