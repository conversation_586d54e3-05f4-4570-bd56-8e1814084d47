import {
  Box, VStack, HStack, Text, Icon, Button,
  useColorModeValue, Badge, Flex, Tooltip,
  Divider
} from '@chakra-ui/react';
import {
  FiTrendingUp, FiAlertCircle, FiAward,
  FiUserPlus, FiDollarSign, FiShoppingBag
} from 'react-icons/fi';

const RecommendationCard = ({ title, description, impact, category, action }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const categoryColors = {
    revenue: 'green',
    engagement: 'blue',
    retention: 'purple',
    acquisition: 'orange'
  };

  const impactColors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  };

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderLeft="4px"
      borderLeftColor={`${categoryColors[category]}.400`}
      boxShadow="sm"
      _hover={{ transform: 'translateX(2px)', transition: 'transform 0.2s' }}
    >
      <HStack justify="space-between" mb={2}>
        <Badge colorScheme={categoryColors[category]}>{category}</Badge>
        <Badge colorScheme={impactColors[impact]}>
          {impact} impact
        </Badge>
      </HStack>
      
      <Text fontSize="sm" fontWeight="medium" mb={1}>
        {title}
      </Text>
      <Text fontSize="sm" color="gray.500" mb={3}>
        {description}
      </Text>
      
      <Button size="sm" colorScheme={categoryColors[category]} variant="outline">
        {action}
      </Button>
    </Box>
  );
};

const SmartRecommendations = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.700');
  
  const recommendations = [
    {
      title: 'Upgrade Premium Tier Pricing',
      description: 'Analysis shows potential for 15% revenue increase with optimized premium tier pricing',
      impact: 'high',
      category: 'revenue',
      action: 'Review Analysis'
    },
    {
      title: 'Customer Retention Campaign',
      description: 'Target at-risk customers in the basic tier with personalized offers',
      impact: 'medium',
      category: 'retention',
      action: 'Launch Campaign'
    },
    {
      title: 'New Feature Adoption',
      description: 'Promote new analytics features to increase engagement with premium users',
      impact: 'medium',
      category: 'engagement',
      action: 'Create Campaign'
    },
    {
      title: 'Lead Generation Opportunity',
      description: 'Identified potential market segment for targeted acquisition',
      impact: 'high',
      category: 'acquisition',
      action: 'View Details'
    }
  ];

  return (
    <Box p={4} bg={bgColor} borderRadius="xl" boxShadow="sm">
      <HStack justify="space-between" mb={4}>
        <Text fontSize="lg" fontWeight="medium">Smart Recommendations</Text>
        <Tooltip label="AI-powered recommendations based on current trends and data analysis">
          <Icon as={FiTrendingUp} color="blue.500" boxSize={5} />
        </Tooltip>
      </HStack>

      <VStack spacing={4} align="stretch">
        {recommendations.map((rec, index) => (
          <RecommendationCard key={index} {...rec} />
        ))}
      </VStack>
    </Box>
  );
};

export default SmartRecommendations;