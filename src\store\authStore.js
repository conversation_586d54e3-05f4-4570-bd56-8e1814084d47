import { create } from 'zustand'

const useAuthStore = create((set) => ({
  user: { email: '<EMAIL>', name: 'Test User' },
  isAuthenticated: true, // Set to true for development
  isLoading: false,
  error: null,

  login: async (credentials) => {
    set({ isLoading: true, error: null })
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      set({
        user: { email: credentials.email, name: '<PERSON>' },
        isAuthenticated: true,
        isLoading: false
      })
    } catch (error) {
      set({ error: error.message, isLoading: false })
    }
  },

  logout: () => {
    set({ user: null, isAuthenticated: false })
  },

  register: async (userData) => {
    set({ isLoading: true, error: null })
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      set({
        user: { email: userData.email, name: userData.username },
        isAuthenticated: true,
        isLoading: false
      })
    } catch (error) {
      set({ error: error.message, isLoading: false })
    }
  }
}))

export default useAuthStore