import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { FiHome, FiArrowLeft } from 'react-icons/fi';

const NotFound = () => {
  const navigate = useNavigate();
  const bgColor = useColorModeValue('gray.50', 'gray.800');
  const textColor = useColorModeValue('gray.600', 'gray.200');

  return (
    <Container maxW="container.xl" py={20}>
      <Box
        bg={bgColor}
        p={8}
        borderRadius="lg"
        textAlign="center"
      >
        <VStack spacing={6}>
          <Heading size="2xl">404</Heading>
          <Heading size="xl">Page Not Found</Heading>
          <Text color={textColor} fontSize="lg">
            The page you're looking for doesn't exist or has been moved.
          </Text>
          <Box pt={6}>
            <VStack spacing={4}>
              <Button
                leftIcon={<FiHome />}
                colorScheme="blue"
                size="lg"
                onClick={() => navigate('/home')}
                width="200px"
              >
                Go Home
              </Button>
              <Button
                leftIcon={<FiArrowLeft />}
                variant="outline"
                size="lg"
                onClick={() => navigate(-1)}
                width="200px"
              >
                Go Back
              </Button>
            </VStack>
          </Box>
        </VStack>
      </Box>
    </Container>
  );
};

export default NotFound;