.auth-container {
  animation: slideUp 0.5s ease-out;
}

.auth-form {
  transition: transform 0.3s ease-in-out;
}

.auth-form:focus-within {
  transform: scale(1.01);
}

.form-input {
  transition: border-color 0.2s ease-in-out;
}

.form-input:focus {
  border-color: #3182ce;
}

.submit-button {
  position: relative;
  overflow: hidden;
}

.submit-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease-out, height 0.3s ease-out;
}

.submit-button:active::after {
  width: 200px;
  height: 200px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-message {
  animation: shake 0.4s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}