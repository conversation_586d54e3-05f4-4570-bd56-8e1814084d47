import {
  Container,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  FormControl,
  FormLabel,
  Input,
  Switch,
  Button,
  Select,
  Stack,
  VStack,
  useToast,
  Divider,
  HStack,
  Text,
  Badge,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Box,
  useColorMode,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react'
import { 
  FiInfo, 
  FiLock, 
  FiGlobe, 
  FiBell, 
  FiUser, 
  FiDatabase,
  FiSun,
  FiMoon,
} from 'react-icons/fi'

function Settings() {
  const toast = useToast()
  const { colorMode, toggleColorMode } = useColorMode()

  const handleSave = () => {
    toast({
      title: 'Settings saved',
      description: 'Your preferences have been updated.',
      status: 'success',
      duration: 3000,
    })
  }

  return (
    <Container maxW="container.xl" py={8}>
      <Tabs variant="enclosed">
        <TabList>
          <Tab><HStack><FiUser /><Text>Profile</Text></HStack></Tab>
          <Tab><HStack><FiBell /><Text>Notifications</Text></HStack></Tab>
          <Tab><HStack><FiLock /><Text>Security</Text></HStack></Tab>
          <Tab><HStack><FiGlobe /><Text>Preferences</Text></HStack></Tab>
          <Tab><HStack><FiDatabase /><Text>Data</Text></HStack></Tab>
        </TabList>

        <TabPanels>
          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">Profile Settings</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel>Display Name</FormLabel>
                    <Input placeholder="Your name" />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Bio</FormLabel>
                    <Input placeholder="Tell us about yourself" />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Profile Visibility</FormLabel>
                    <Select>
                      <option value="public">Public</option>
                      <option value="private">Private</option>
                      <option value="contacts">Contacts Only</option>
                    </Select>
                  </FormControl>
                </VStack>
              </CardBody>
            </Card>
          </TabPanel>

          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">Notification Preferences</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel>Email Notifications</FormLabel>
                    <Stack spacing={2}>
                      <HStack justify="space-between">
                        <Text>Project updates</Text>
                        <Switch defaultChecked />
                      </HStack>
                      <HStack justify="space-between">
                        <Text>Security alerts</Text>
                        <Switch defaultChecked />
                      </HStack>
                      <HStack justify="space-between">
                        <Text>Marketing emails</Text>
                        <Switch />
                      </HStack>
                    </Stack>
                  </FormControl>
                  
                  <Divider />
                  
                  <FormControl>
                    <FormLabel>Push Notifications</FormLabel>
                    <Stack spacing={2}>
                      <HStack justify="space-between">
                        <Text>New messages</Text>
                        <Switch defaultChecked />
                      </HStack>
                      <HStack justify="space-between">
                        <Text>Task assignments</Text>
                        <Switch defaultChecked />
                      </HStack>
                    </Stack>
                  </FormControl>
                </VStack>
              </CardBody>
            </Card>
          </TabPanel>

          <TabPanel>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              <Card>
                <CardHeader>
                  <Heading size="md">Security Settings</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <FormControl>
                      <FormLabel>Change Password</FormLabel>
                      <Input type="password" placeholder="Current password" mb={2} />
                      <Input type="password" placeholder="New password" mb={2} />
                      <Input type="password" placeholder="Confirm new password" />
                    </FormControl>
                  </VStack>
                </CardBody>
              </Card>

              <Card>
                <CardHeader>
                  <Heading size="md">Two-Factor Authentication</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text>2FA Status</Text>
                        <Badge colorScheme="green">Enabled</Badge>
                      </VStack>
                      <Switch defaultChecked />
                    </HStack>
                    <Button colorScheme="blue" variant="outline">
                      Configure 2FA
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>
          </TabPanel>

          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">System Preferences</Heading>
              </CardHeader>
              <CardBody>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  <FormControl>
                    <FormLabel>Language</FormLabel>
                    <Select defaultValue="en">
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="zh">Chinese</option>
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Time Zone</FormLabel>
                    <Select defaultValue="utc">
                      <option value="utc">UTC</option>
                      <option value="pst">Pacific Time</option>
                      <option value="est">Eastern Time</option>
                      <option value="gmt">GMT</option>
                    </Select>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <HStack>
                      <Icon 
                        as={colorMode === 'light' ? FiSun : FiMoon} 
                        color={colorMode === 'light' ? 'yellow.500' : 'blue.200'}
                      />
                      <FormLabel mb={0}>Dark Mode</FormLabel>
                    </HStack>
                    <Switch
                      isChecked={colorMode === 'dark'}
                      onChange={toggleColorMode}
                      colorScheme="brand"
                    />
                  </FormControl>
                  <Text fontSize="sm" color="gray.500">
                    Choose between light and dark mode for your interface
                  </Text>
                </SimpleGrid>
              </CardBody>
            </Card>
          </TabPanel>

          <TabPanel>
            <Card>
              <CardHeader>
                <Heading size="md">Data Management</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel>Data Export</FormLabel>
                    <Button colorScheme="blue" variant="outline">
                      Export All Data
                    </Button>
                  </FormControl>
                  
                  <Divider />
                  
                  <FormControl>
                    <FormLabel>Account Deletion</FormLabel>
                    <Button colorScheme="red" variant="outline">
                      Delete Account
                    </Button>
                  </FormControl>
                </VStack>
              </CardBody>
            </Card>
          </TabPanel>
        </TabPanels>
      </Tabs>

      <Box mt={8} textAlign="right">
        <Button colorScheme="blue" onClick={handleSave}>
          Save Changes
        </Button>
      </Box>
    </Container>
  )
}

export default Settings



