# NexusFlow E-commerce Backend

A comprehensive Node.js/Express backend for the NexusFlow e-commerce system with MongoDB integration, authentication, and multiple payment gateways.

## 🚀 Features

### 🔐 Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (Admin, Moderator, User)
- Email verification system
- Password reset functionality
- Account lockout protection
- Rate limiting for security

### 💳 Payment Integration
- **Stripe** - Credit/Debit card processing
- **PayPal** - PayPal payments and wallet
- **M-Pesa** - Mobile money integration for Kenya
- Webhook handling for payment confirmations
- Transaction logging and monitoring

### 📊 Database Models
- **User Management** - Complete user profiles with preferences
- **Product Catalog** - Products with variants, inventory, and reviews
- **Order Management** - Full order lifecycle with tracking
- **Category System** - Hierarchical product categorization

### 📈 Dashboard & Analytics
- Real-time analytics and metrics
- Performance monitoring
- Customer insights
- System health monitoring
- Live data updates via Socket.IO

### 🛡️ Security Features
- Helmet.js for security headers
- Rate limiting and DDoS protection
- Input sanitization and XSS protection
- MongoDB injection prevention
- Comprehensive error handling

## 🛠️ Installation

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local or Atlas)
- Redis (optional, for caching)

### Setup Steps

1. **Install Dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   # Database
   MONGODB_URI=mongodb://localhost:27017/nexusflow_ecommerce
   
   # JWT Secrets
   JWT_SECRET=your-super-secret-jwt-key
   JWT_REFRESH_SECRET=your-refresh-secret-key
   
   # Payment Gateways
   STRIPE_SECRET_KEY=sk_test_your_stripe_key
   PAYPAL_CLIENT_ID=your_paypal_client_id
   MPESA_CONSUMER_KEY=your_mpesa_consumer_key
   ```

3. **Database Setup**
   ```bash
   # Seed database with sample data
   npm run seed
   
   # Or clear and reseed
   npm run db:reset
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/register     - Register new user
POST /api/auth/login        - User login
POST /api/auth/logout       - User logout
GET  /api/auth/me          - Get current user
```

### Payment Endpoints
```
POST /api/payments/stripe/create-intent  - Create Stripe payment intent
POST /api/payments/stripe/confirm        - Confirm Stripe payment
POST /api/payments/paypal/create         - Create PayPal payment
POST /api/payments/paypal/execute        - Execute PayPal payment
POST /api/payments/mpesa/stk-push        - Initiate M-Pesa STK push
GET  /api/payments/mpesa/status/:id      - Check M-Pesa payment status
```

### Product Endpoints
```
GET    /api/products           - Get all products (with filters)
GET    /api/products/:id       - Get single product
POST   /api/products           - Create product (Admin)
PUT    /api/products/:id       - Update product (Admin)
DELETE /api/products/:id       - Delete product (Admin)
POST   /api/products/:id/reviews - Add product review
```

### Order Endpoints
```
POST /api/orders              - Create new order
GET  /api/orders/my-orders    - Get user orders
GET  /api/orders/:id          - Get single order
PUT  /api/orders/:id/status   - Update order status (Admin)
PUT  /api/orders/:id/cancel   - Cancel order
```

### Dashboard Endpoints
```
GET /api/dashboard/analytics   - Get analytics data (Admin)
GET /api/dashboard/realtime    - Get real-time data (Admin)
GET /api/dashboard/performance - Get performance metrics (Admin)
GET /api/dashboard/health      - Get system health (Admin)
GET /api/dashboard/customers   - Get customer insights (Admin)
```

## 🔧 Configuration

### MongoDB Setup
1. **Local MongoDB:**
   ```bash
   # Install MongoDB Community Edition
   # Start MongoDB service
   mongod --dbpath /path/to/your/db
   ```

2. **MongoDB Atlas (Cloud):**
   - Create account at mongodb.com
   - Create cluster and get connection string
   - Update MONGODB_URI in .env

### Payment Gateway Setup

#### Stripe
1. Create account at stripe.com
2. Get API keys from dashboard
3. Add to .env file

#### PayPal
1. Create developer account at developer.paypal.com
2. Create sandbox/live application
3. Get client ID and secret

#### M-Pesa (Safaricom)
1. Register at developer.safaricom.co.ke
2. Create Lipa Na M-Pesa Online app
3. Get consumer key, secret, and passkey

## 🚀 Deployment

### Environment Variables for Production
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://user:<EMAIL>/db
JWT_SECRET=your-production-jwt-secret
# ... other production configs
```

### Docker Deployment
```dockerfile
# Dockerfile example
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

## 📊 Monitoring & Logging

- **Winston** for structured logging
- **Performance metrics** tracking
- **Error monitoring** with stack traces
- **Payment transaction** logging
- **Security event** logging

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 🔒 Security Best Practices

- JWT tokens with expiration
- Password hashing with bcrypt
- Rate limiting on sensitive endpoints
- Input validation and sanitization
- CORS configuration
- Helmet.js security headers
- MongoDB injection prevention

## 📞 Support

For issues and questions:
- Email: <EMAIL>
- Documentation: [API Docs](http://localhost:5000/api/docs)
- GitHub Issues: [Report Bug](https://github.com/your-repo/issues)

## 📄 License

MIT License - see LICENSE file for details.
