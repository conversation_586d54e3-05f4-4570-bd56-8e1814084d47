import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { logger } from './utils/logger.js';
import { seedDatabase } from './seeders/index.js';

dotenv.config();

const setup = async () => {
  try {
    logger.info('🚀 Starting NexusFlow Backend Setup...');

    // 1. Check environment variables
    logger.info('📋 Checking environment configuration...');
    const requiredEnvVars = [
      'MONGODB_URI',
      'JWT_SECRET',
      'JWT_REFRESH_SECRET'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      logger.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
      logger.info('💡 Please copy .env.example to .env and fill in the required values');
      process.exit(1);
    }

    logger.info('✅ Environment configuration looks good');

    // 2. Test database connection
    logger.info('🔌 Testing database connection...');
    try {
      await mongoose.connect(process.env.MONGODB_URI);
      logger.info('✅ Database connection successful');
    } catch (dbError) {
      logger.error('❌ Database connection failed:', dbError.message);
      logger.info('💡 Make sure MongoDB is running and the connection string is correct');
      process.exit(1);
    }

    // 3. Check if database is empty
    const collections = await mongoose.connection.db.listCollections().toArray();
    const hasData = collections.some(collection => 
      ['users', 'products', 'categories'].includes(collection.name)
    );

    if (!hasData) {
      logger.info('📦 Database appears to be empty. Seeding with sample data...');
      await seedDatabase();
      logger.info('✅ Sample data seeded successfully');
    } else {
      logger.info('📊 Database already contains data');
    }

    // 4. Verify payment gateway configuration
    logger.info('💳 Checking payment gateway configuration...');
    
    const paymentGateways = {
      stripe: process.env.STRIPE_SECRET_KEY,
      paypal: process.env.PAYPAL_CLIENT_ID && process.env.PAYPAL_CLIENT_SECRET,
      mpesa: process.env.MPESA_CONSUMER_KEY && process.env.MPESA_CONSUMER_SECRET
    };

    Object.entries(paymentGateways).forEach(([gateway, configured]) => {
      if (configured) {
        logger.info(`✅ ${gateway.toUpperCase()} configuration found`);
      } else {
        logger.warn(`⚠️  ${gateway.toUpperCase()} not configured (optional)`);
      }
    });

    // 5. Setup complete
    logger.info('🎉 Backend setup completed successfully!');
    logger.info('');
    logger.info('📝 Next steps:');
    logger.info('   1. Start the development server: npm run dev');
    logger.info('   2. The API will be available at: http://localhost:5000');
    logger.info('   3. Check the logs for any issues');
    logger.info('');
    logger.info('🔗 Available endpoints:');
    logger.info('   - Health check: GET /api/health');
    logger.info('   - Authentication: POST /api/auth/login');
    logger.info('   - Products: GET /api/products');
    logger.info('   - Dashboard: GET /api/dashboard/analytics');
    logger.info('');
    logger.info('👤 Default admin account:');
    logger.info('   - Email: <EMAIL>');
    logger.info('   - Password: Admin123!');

    process.exit(0);

  } catch (error) {
    logger.error('❌ Setup failed:', error);
    process.exit(1);
  }
};

// Run setup if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setup();
}

export default setup;
