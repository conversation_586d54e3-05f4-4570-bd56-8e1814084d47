import {
  <PERSON>, VStack, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Icon, Badge,
  useColorModeValue, Popover, PopoverTrigger,
  PopoverContent, PopoverHeader, PopoverBody,
  IconButton, Button
} from '@chakra-ui/react';
import { FiBell, FiCheck } from 'react-icons/fi';
import { useState } from 'react';

const NotificationsCenter = () => {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title: 'System Update',
      message: 'New version 2.0 is available for update',
      time: '2 minutes ago',
      type: 'info',
      read: false
    },
    {
      id: 2,
      title: 'Security Alert',
      message: 'Unusual login attempt detected',
      time: '5 minutes ago',
      type: 'warning',
      read: false
    },
    {
      id: 3,
      title: 'Backup Complete',
      message: 'Daily backup completed successfully',
      time: '10 minutes ago',
      type: 'success',
      read: true
    }
  ]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, read: true })));
  };

  return (
    <Box position="fixed" bottom={4} right={4}>
      <Popover placement="top-end">
        <PopoverTrigger>
          <IconButton
            icon={<FiBell />}
            size="lg"
            colorScheme="blue"
            variant="solid"
            position="relative"
          >
            {unreadCount > 0 && (
              <Badge
                position="absolute"
                top={-1}
                right={-1}
                colorScheme="red"
                borderRadius="full"
                size="sm"
              >
                {unreadCount}
              </Badge>
            )}
          </IconButton>
        </PopoverTrigger>
        <PopoverContent>
          <PopoverHeader>
            <HStack justify="space-between">
              <Text fontWeight="medium">Notifications</Text>
              <Button
                size="sm"
                leftIcon={<FiCheck />}
                variant="ghost"
                onClick={markAllAsRead}
              >
                Mark all as read
              </Button>
            </HStack>
          </PopoverHeader>
          <PopoverBody>
            <VStack spacing={3} align="stretch">
              {notifications.map((notif) => (
                <Box
                  key={notif.id}
                  p={2}
                  bg={notif.read ? 'transparent' : useColorModeValue('gray.50', 'gray.700')}
                  borderRadius="md"
                >
                  <Text fontWeight="medium">{notif.title}</Text>
                  <Text fontSize="sm" color="gray.500">{notif.message}</Text>
                  <Text fontSize="xs" color="gray.400" mt={1}>{notif.time}</Text>
                </Box>
              ))}
            </VStack>
          </PopoverBody>
        </PopoverContent>
      </Popover>
    </Box>
  );
};

export default NotificationsCenter;