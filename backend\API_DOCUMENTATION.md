# NexusFlow API Documentation

## Base URL
```
Development: http://localhost:5000/api
Production: https://your-domain.com/api
```

## Authentication

All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this format:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": { ... },
  "errors": [ ... ] // Only for validation errors
}
```

## Authentication Endpoints

### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "username": "johndo<PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+254700000000"
}
```

### Login User
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

### Get Current User
```http
GET /auth/me
Authorization: Bearer <token>
```

## Payment Endpoints

### Stripe Payment Intent
```http
POST /payments/stripe/create-intent
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 299.99,
  "currency": "usd",
  "orderId": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

### M-Pesa STK Push
```http
POST /payments/mpesa/stk-push
Authorization: Bearer <token>
Content-Type: application/json

{
  "phoneNumber": "254700000000",
  "amount": 1000,
  "orderId": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

### PayPal Payment
```http
POST /payments/paypal/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 299.99,
  "currency": "USD",
  "orderId": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

## Product Endpoints

### Get Products
```http
GET /products?page=1&limit=12&category=electronics&search=headphones&sort=price_low
```

Query Parameters:
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 12)
- `category` (string): Category ID filter
- `search` (string): Search term
- `sort` (string): Sort option (price_low, price_high, rating, newest, popular)
- `minPrice` (number): Minimum price filter
- `maxPrice` (number): Maximum price filter
- `brand` (string): Brand filter
- `featured` (boolean): Featured products only

### Get Single Product
```http
GET /products/:id
```

### Create Product (Admin)
```http
POST /products
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "name": "Premium Headphones",
  "description": "High-quality wireless headphones",
  "price": 299.99,
  "category": "60f7b3b3b3b3b3b3b3b3b3b3",
  "inventory": {
    "quantity": 50,
    "trackQuantity": true
  },
  "images": [{
    "url": "https://example.com/image.jpg",
    "alt": "Product image",
    "isPrimary": true
  }]
}
```

## Order Endpoints

### Create Order
```http
POST /orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    {
      "product": "60f7b3b3b3b3b3b3b3b3b3b3",
      "quantity": 2,
      "attributes": {
        "color": "black",
        "size": "large"
      }
    }
  ],
  "shippingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "street": "123 Main St",
    "city": "Nairobi",
    "state": "Nairobi",
    "zipCode": "00100",
    "country": "Kenya",
    "phone": "+254700000000"
  },
  "shipping": {
    "method": "standard",
    "cost": 500
  }
}
```

### Get User Orders
```http
GET /orders/my-orders?page=1&limit=10&status=pending
Authorization: Bearer <token>
```

### Update Order Status (Admin)
```http
PUT /orders/:id/status
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "status": "shipped",
  "note": "Order shipped via DHL"
}
```

## Dashboard Endpoints

### Get Analytics
```http
GET /dashboard/analytics?period=month
Authorization: Bearer <admin-token>
```

Period options: `week`, `month`, `quarter`, `year`

### Get Real-time Data
```http
GET /dashboard/realtime
Authorization: Bearer <admin-token>
```

### Get System Health
```http
GET /dashboard/health
Authorization: Bearer <admin-token>
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `INVALID_TOKEN` | JWT token is invalid |
| `TOKEN_EXPIRED` | JWT token has expired |
| `ACCOUNT_LOCKED` | User account is locked |
| `EMAIL_NOT_VERIFIED` | Email verification required |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `DUPLICATE_FIELD` | Unique field constraint violation |
| `PAYMENT_ERROR` | Payment processing failed |
| `RATE_LIMIT_EXCEEDED` | Too many requests |

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `423` - Locked
- `429` - Too Many Requests
- `500` - Internal Server Error

## Webhooks

### M-Pesa Callback
```http
POST /payments/mpesa/callback
Content-Type: application/json

{
  "Body": {
    "stkCallback": {
      "MerchantRequestID": "...",
      "CheckoutRequestID": "...",
      "ResultCode": 0,
      "ResultDesc": "The service request is processed successfully.",
      "CallbackMetadata": {
        "Item": [
          {
            "Name": "Amount",
            "Value": 1000
          },
          {
            "Name": "MpesaReceiptNumber",
            "Value": "NLJ7RT61SV"
          }
        ]
      }
    }
  }
}
```

## Real-time Events (Socket.IO)

### Client Events
```javascript
// Join user room for notifications
socket.emit('join-room', `user_${userId}`);
```

### Server Events
```javascript
// Payment success notification
socket.on('payment_success', (data) => {
  console.log('Payment successful:', data);
});

// Order status update
socket.on('order_status_updated', (data) => {
  console.log('Order status updated:', data);
});

// New order notification (admin)
socket.on('new_order', (data) => {
  console.log('New order received:', data);
});
```

## Rate Limiting

- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 5 requests per 15 minutes per IP
- **User-specific**: 100 requests per 15 minutes per user

## File Upload

Maximum file size: 5MB
Allowed types: JPEG, PNG, GIF, WebP

## Pagination

All list endpoints support pagination:
```json
{
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "pages": 10
    }
  }
}
```

## Search

Products support full-text search:
```http
GET /products?search=wireless headphones
```

## Filtering

Products support multiple filters:
```http
GET /products?category=electronics&minPrice=100&maxPrice=500&brand=apple
```
