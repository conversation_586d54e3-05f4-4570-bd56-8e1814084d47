import { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Icon,
  Divider,
  SimpleGrid,
  Image
} from '@chakra-ui/react';
import { 
  FiSun, 
  FiCloud, 
  FiCloudRain, 
  FiCloudSnow, 
  FiCloudLightning,
  FiWind,
  FiDroplet,
  FiMapPin
} from 'react-icons/fi';

// Sample weather data
const weatherData = {
  location: 'New York, NY',
  current: {
    temp: 72,
    condition: 'Partly Cloudy',
    icon: 'cloud',
    humidity: 65,
    windSpeed: 8,
    windDirection: 'NE',
    feelsLike: 74,
    uvIndex: 5
  },
  forecast: [
    { day: 'Mon', temp: 75, icon: 'sun' },
    { day: 'Tue', temp: 72, icon: 'cloud' },
    { day: 'Wed', temp: 68, icon: 'cloud-rain' },
    { day: 'Thu', temp: 70, icon: 'cloud' },
    { day: 'Fri', temp: 74, icon: 'sun' }
  ]
};

const getWeatherIcon = (icon) => {
  switch(icon) {
    case 'sun': return FiSun;
    case 'cloud': return FiCloud;
    case 'cloud-rain': return FiCloudRain;
    case 'cloud-snow': return FiCloudSnow;
    case 'cloud-lightning': return FiCloudLightning;
    default: return FiSun;
  }
};

const WeatherWidget = () => {
  const [weather, setWeather] = useState(weatherData);
  const [time, setTime] = useState(new Date());
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const gradientBg = useColorModeValue(
    'linear(to-br, blue.50, blue.100)',
    'linear(to-br, blue.900, purple.900)'
  );
  
  // Update time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setTime(new Date());
    }, 60000);
    
    return () => clearInterval(interval);
  }, []);
  
  const getWeatherColor = (icon) => {
    switch(icon) {
      case 'sun': return 'yellow.500';
      case 'cloud': return 'blue.400';
      case 'cloud-rain': return 'blue.600';
      case 'cloud-snow': return 'blue.200';
      case 'cloud-lightning': return 'purple.500';
      default: return 'yellow.500';
    }
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
      overflow="hidden"
    >
      <Box 
        bgGradient={gradientBg}
        p={4}
      >
        <Flex justify="space-between" align="flex-start">
          <Box>
            <HStack>
              <Icon as={FiMapPin} />
              <Text fontWeight="medium">{weather.location}</Text>
            </HStack>
            <Text fontSize="sm" color={textColor}>
              {time.toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric' })}
            </Text>
            <Text fontSize="sm" color={textColor}>
              {time.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </Box>
          <Icon 
            as={getWeatherIcon(weather.current.icon)} 
            boxSize={12}
            color={getWeatherColor(weather.current.icon)}
          />
        </Flex>
        
        <Flex align="baseline" mt={2}>
          <Text fontSize="4xl" fontWeight="bold">{weather.current.temp}°</Text>
          <Text ml={2} fontWeight="medium">{weather.current.condition}</Text>
        </Flex>
        
        <HStack mt={2} fontSize="sm" color={textColor}>
          <Icon as={FiDroplet} />
          <Text>{weather.current.humidity}% humidity</Text>
          <Icon as={FiWind} ml={2} />
          <Text>{weather.current.windSpeed} mph {weather.current.windDirection}</Text>
        </HStack>
      </Box>
      
      <Divider />
      
      <SimpleGrid columns={5} p={4}>
        {weather.forecast.map((day, index) => (
          <VStack key={index} spacing={1}>
            <Text fontWeight="medium">{day.day}</Text>
            <Icon 
              as={getWeatherIcon(day.icon)} 
              boxSize={6}
              color={getWeatherColor(day.icon)}
            />
            <Text>{day.temp}°</Text>
          </VStack>
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default WeatherWidget;
