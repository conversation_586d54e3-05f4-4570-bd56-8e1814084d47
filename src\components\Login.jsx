import { useState } from 'react'
import { useNavigate, Link as RouterLink } from 'react-router-dom'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Text,
  FormErrorMessage,
  useToast,
  Link,
} from '@chakra-ui/react'
import { authStyles as styles } from '../styles/auth'
import useAuthStore from '../store/authStore'
import Loading from './Loading'
import '../styles/Login.css'

const LoginSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Required'),
})

function Login() {
  const [isLoading, setIsLoading] = useState(false)
  const navigate = useNavigate()
  const toast = useToast()
  const login = useAuthStore((state) => state.login)

  const handleSubmit = async (values, actions) => {
    setIsLoading(true)
    try {
      await login(values)
      navigate('/dashboard')
      toast({
        title: 'Welcome back!',
        status: 'success',
        duration: 3000,
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 5000,
      })
    } finally {
      setIsLoading(false)
      actions.setSubmitting(false)
    }
  }

  if (isLoading) return <Loading />

  return (
    <Box {...styles.container} className="login-container">
      <VStack {...styles.formStack} className="login-form">
        <Text {...styles.header}>Welcome Back</Text>
        <Formik
          initialValues={{ email: '', password: '' }}
          validationSchema={LoginSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched }) => (
            <Form style={{ width: '100%' }}>
              <VStack spacing={4}>
                <FormControl
                  isInvalid={errors.email && touched.email}
                  className="form-field"
                >
                  <FormLabel color="whiteAlpha.900">Email</FormLabel>
                  <Field
                    as={Input}
                    name="email"
                    type="email"
                    placeholder="Enter your email"
                  />
                  <FormErrorMessage>{errors.email}</FormErrorMessage>
                </FormControl>

                <FormControl
                  isInvalid={errors.password && touched.password}
                  className="form-field"
                >
                  <FormLabel color="whiteAlpha.900">Password</FormLabel>
                  <Field
                    as={Input}
                    name="password"
                    type="password"
                    placeholder="Enter your password"
                  />
                  <FormErrorMessage>{errors.password}</FormErrorMessage>
                </FormControl>

                <Button
                  type="submit"
                  className="submit-button"
                  {...styles.submitButton}
                >
                  Login
                </Button>
              </VStack>
            </Form>
          )}
        </Formik>
        <Text {...styles.linkText}>
          Don't have an account?{' '}
          <Link as={RouterLink} to="/register" color="blue.200">
            Register here
          </Link>
        </Text>
      </VStack>
    </Box>
  )
}

export default Login



