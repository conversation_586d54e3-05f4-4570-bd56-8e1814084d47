import {
  Box, VStack, HStack, Text, Icon, Progress,
  useColorModeValue, Grid, GridItem, Stat,
  StatLabel, StatNumber, StatHelpText,
  StatArrow, Flex, Tooltip
} from '@chakra-ui/react';
import {
  FiUsers, FiTrendingUp, FiRepeat,
  FiUserPlus, FiUserMinus, FiInfo
} from 'react-icons/fi';

const MetricBox = ({ title, value, change, icon, color }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  
  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      _hover={{ transform: 'translateY(-2px)', transition: 'transform 0.2s' }}
    >
      <HStack spacing={3} mb={2}>
        <Icon as={icon} color={`${color}.500`} boxSize={5} />
        <Text fontSize="sm" color="gray.500">{title}</Text>
      </HStack>
      <Stat>
        <StatNumber fontSize="2xl">{value}</StatNumber>
        <StatHelpText>
          <StatArrow type={change >= 0 ? 'increase' : 'decrease'} />
          {Math.abs(change)}%
        </StatHelpText>
      </Stat>
    </Box>
  );
};

const CustomerInsights = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.700');
  const metrics = [
    {
      title: 'Active Customers',
      value: '1,234',
      change: 12,
      icon: FiUsers,
      color: 'blue'
    },
    {
      title: 'Customer Growth',
      value: '156',
      change: 8,
      icon: FiUserPlus,
      color: 'green'
    },
    {
      title: 'Churn Rate',
      value: '2.4%',
      change: -1.5,
      icon: FiUserMinus,
      color: 'red'
    },
    {
      title: 'Repeat Rate',
      value: '68%',
      change: 5,
      icon: FiRepeat,
      color: 'purple'
    }
  ];

  const segments = [
    { name: 'Premium', percentage: 35, color: 'green.400' },
    { name: 'Standard', percentage: 45, color: 'blue.400' },
    { name: 'Basic', percentage: 20, color: 'gray.400' }
  ];

  return (
    <Box p={4} bg={bgColor} borderRadius="xl" boxShadow="sm">
      <Text fontSize="lg" fontWeight="medium" mb={4}>Customer Insights</Text>
      
      <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={4} mb={6}>
        {metrics.map((metric, index) => (
          <GridItem key={index}>
            <MetricBox {...metric} />
          </GridItem>
        ))}
      </Grid>

      <Box bg={useColorModeValue('white', 'gray.800')} p={4} borderRadius="lg">
        <HStack justify="space-between" mb={4}>
          <Text fontSize="sm" fontWeight="medium">Customer Segments</Text>
          <Tooltip label="Distribution of customer segments based on subscription type">
            <Icon as={FiInfo} color="gray.500" cursor="pointer" />
          </Tooltip>
        </HStack>
        <VStack spacing={4} align="stretch">
          {segments.map((segment, index) => (
            <Box key={index}>
              <Flex justify="space-between" mb={1}>
                <Text fontSize="sm">{segment.name}</Text>
                <Text fontSize="sm" color="gray.500">{segment.percentage}%</Text>
              </Flex>
              <Progress
                value={segment.percentage}
                colorScheme={segment.color.split('.')[0]}
                size="sm"
                borderRadius="full"
              />
            </Box>
          ))}
        </VStack>
      </Box>
    </Box>
  );
};

export default CustomerInsights;