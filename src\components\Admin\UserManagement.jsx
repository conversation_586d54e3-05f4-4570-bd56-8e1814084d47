import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  HStack,
  useToast,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Input,
  Select,
  FormControl,
  FormLabel,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  Button,
  Switch,
  Text
} from '@chakra-ui/react';
import { FiEdit2, FiTrash2, FiMoreVertical, FiPlus, FiLock, FiUnlock } from 'react-icons/fi';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      const mockUsers = [
        { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'active' },
        { id: 2, name: 'Jane <PERSON>', email: '<EMAIL>', role: 'admin', status: 'active' },
      ];
      setUsers(mockUsers);
    } catch (error) {
      toast({
        title: 'Error fetching users',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (user) => {
    // Implement edit functionality
    console.log('Edit user:', user);
  };

  const handleDelete = (userId) => {
    // Implement delete functionality
    console.log('Delete user:', userId);
  };

  const handleToggleStatus = (userId, currentStatus) => {
    // Implement status toggle functionality
    console.log('Toggle status for user:', userId, 'Current status:', currentStatus);
  };

  const handleRoleChange = (userId, newRole) => {
    // Implement role change functionality
    console.log('Change role for user:', userId, 'New role:', newRole);
  };

  return (
    <Box p={4}>
      <HStack justify="space-between" mb={6}>
        <Button
          leftIcon={<FiPlus />}
          colorScheme="blue"
          onClick={onOpen}
        >
          Add User
        </Button>
      </HStack>

      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Email</Th>
            <Th>Role</Th>
            <Th>Status</Th>
            <Th>Actions</Th>
          </Tr>
        </Thead>
        <Tbody>
          {users.map((user) => (
            <Tr key={user.id}>
              <Td>{user.name}</Td>
              <Td>{user.email}</Td>
              <Td>
                <Badge 
                  colorScheme={user.role === 'admin' ? 'purple' : 'blue'}
                >
                  {user.role}
                </Badge>
              </Td>
              <Td>
                <Badge 
                  colorScheme={user.status === 'active' ? 'green' : 'red'}
                >
                  {user.status}
                </Badge>
              </Td>
              <Td>
                <HStack spacing={2}>
                  <IconButton
                    icon={<FiEdit2 />}
                    aria-label="Edit user"
                    size="sm"
                    onClick={() => handleEdit(user)}
                  />
                  <IconButton
                    icon={user.status === 'active' ? <FiLock /> : <FiUnlock />}
                    aria-label="Toggle status"
                    size="sm"
                    onClick={() => handleToggleStatus(user.id, user.status)}
                  />
                  <Menu>
                    <MenuButton
                      as={IconButton}
                      icon={<FiMoreVertical />}
                      variant="ghost"
                      size="sm"
                    />
                    <MenuList>
                      <MenuItem
                        onClick={() => handleRoleChange(user.id, user.role === 'admin' ? 'user' : 'admin')}
                      >
                        Change to {user.role === 'admin' ? 'User' : 'Admin'}
                      </MenuItem>
                      <MenuItem 
                        icon={<FiTrash2 />}
                        color="red.500"
                        onClick={() => handleDelete(user.id)}
                      >
                        Delete
                      </MenuItem>
                    </MenuList>
                  </Menu>
                </HStack>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>

      {/* Add/Edit User Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Add New User</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <FormControl mb={4}>
              <FormLabel>Name</FormLabel>
              <Input placeholder="Enter user name" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Email</FormLabel>
              <Input type="email" placeholder="Enter email" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Role</FormLabel>
              <Select placeholder="Select role">
                <option value="user">User</option>
                <option value="admin">Admin</option>
              </Select>
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Status</FormLabel>
              <HStack>
                <Switch defaultChecked />
                <Text>Active</Text>
              </HStack>
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue">Save</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default UserManagement;