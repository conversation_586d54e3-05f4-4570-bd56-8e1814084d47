// Mock data for development
const mockProducts = [
  {
    id: '1',
    name: 'Premium Headphones',
    price: 299.99,
    description: 'High-quality wireless headphones with noise cancellation',
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
    rating: 4.5,
    sales: 1200,
    category: 'electronics',
    specifications: [
      { name: 'Battery Life', value: '20 hours' },
      { name: 'Wireless Range', value: '30 feet' }
    ],
    variants: [
      { id: '1-1', name: 'Matte Black', price: 299.99, inStock: true },
      { id: '1-2', name: '<PERSON>', price: 309.99, inStock: true },
      { id: '1-3', name: 'Rose Gold', price: 319.99, inStock: false }
    ]
  },
  {
    id: '2',
    name: 'Smart Watch Pro',
    price: 399.99,
    description: 'Advanced smartwatch with health monitoring and GPS',
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500',
    rating: 4.7,
    sales: 890,
    category: 'electronics',
    specifications: [
      { name: 'Battery Life', value: '5 days' },
      { name: 'Water Resistance', value: '50m' }
    ],
    variants: [
      { id: '2-1', name: 'Space Gray', price: 399.99, inStock: true },
      { id: '2-2', name: 'Silver', price: 399.99, inStock: true }
    ]
  },
  {
    id: '3',
    name: 'Professional Camera',
    price: 1299.99,
    description: 'Full-frame mirrorless camera for professional photography',
    image: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=500',
    rating: 4.8,
    sales: 450,
    category: 'electronics',
    specifications: [
      { name: 'Sensor', value: 'Full-frame CMOS' },
      { name: 'Resolution', value: '45.7 MP' }
    ]
  },
  {
    id: '4',
    name: 'Designer Leather Bag',
    price: 599.99,
    description: 'Handcrafted premium leather shoulder bag',
    image: 'https://images.unsplash.com/photo-1548036328-c9fa89d128fa?w=500',
    rating: 4.6,
    sales: 320,
    category: 'accessories',
    variants: [
      { id: '4-1', name: 'Brown', price: 599.99, inStock: true },
      { id: '4-2', name: 'Black', price: 599.99, inStock: true },
      { id: '4-3', name: 'Tan', price: 619.99, inStock: true }
    ]
  },
  {
    id: '5',
    name: 'Ultra HD Smart TV',
    price: 1499.99,
    description: '65-inch 4K OLED Smart TV with HDR',
    image: 'https://images.unsplash.com/photo-1593784991095-a205069533cd?w=500',
    rating: 4.9,
    sales: 280,
    category: 'electronics',
    specifications: [
      { name: 'Screen Size', value: '65 inches' },
      { name: 'Resolution', value: '4K Ultra HD' }
    ]
  },
  {
    id: '6',
    name: 'Luxury Watch',
    price: 2999.99,
    description: 'Swiss-made automatic luxury timepiece',
    image: 'https://images.unsplash.com/photo-1523170335258-f5ed11844a49?w=500',
    rating: 4.9,
    sales: 150,
    category: 'accessories',
    specifications: [
      { name: 'Movement', value: 'Automatic' },
      { name: 'Water Resistance', value: '100m' }
    ]
  },
  {
    id: '7',
    name: 'Gaming Laptop',
    price: 1899.99,
    description: 'High-performance gaming laptop with RTX graphics',
    image: 'https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=500',
    rating: 4.7,
    sales: 420,
    category: 'electronics',
    specifications: [
      { name: 'GPU', value: 'RTX 3080' },
      { name: 'RAM', value: '32GB' }
    ]
  },
  {
    id: '8',
    name: 'Designer Sunglasses',
    price: 299.99,
    description: 'Premium polarized sunglasses',
    image: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500',
    rating: 4.5,
    sales: 680,
    category: 'accessories',
    variants: [
      { id: '8-1', name: 'Classic Black', price: 299.99, inStock: true },
      { id: '8-2', name: 'Tortoise Shell', price: 299.99, inStock: true }
    ]
  },
  {
    id: '9',
    name: 'Wireless Earbuds Pro',
    price: 249.99,
    description: 'Premium wireless earbuds with active noise cancellation',
    image: 'https://images.unsplash.com/photo-1588423771073-b8903fbb85b5?w=500',
    rating: 4.6,
    sales: 950,
    category: 'electronics',
    specifications: [
      { name: 'Battery Life', value: '8 hours' },
      { name: 'Charging Case', value: '24 hours' }
    ]
  },
  {
    id: '10',
    name: 'Smart Home Hub',
    price: 199.99,
    description: 'Central control for your smart home devices',
    image: 'https://images.unsplash.com/photo-1558089687-db5ff6e5ce28?w=500',
    rating: 4.4,
    sales: 720,
    category: 'electronics',
    specifications: [
      { name: 'Compatibility', value: 'Multiple platforms' },
      { name: 'Voice Control', value: 'Yes' }
    ]
  },
  {
    id: '11',
    name: 'Premium Backpack',
    price: 159.99,
    description: 'Water-resistant laptop backpack with anti-theft features',
    image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=500',
    rating: 4.7,
    sales: 840,
    category: 'accessories',
    specifications: [
      { name: 'Capacity', value: '30L' },
      { name: 'Laptop Compartment', value: 'Up to 17 inch' }
    ]
  },
  {
    id: '12',
    name: 'Fitness Tracker',
    price: 129.99,
    description: 'Advanced fitness and health monitoring device',
    image: 'https://images.unsplash.com/photo-**********-e6d1684e0944?w=500',
    rating: 4.5,
    sales: 1100,
    category: 'electronics',
    variants: [
      { id: '12-1', name: 'Black', price: 129.99, inStock: true },
      { id: '12-2', name: 'Blue', price: 129.99, inStock: true },
      { id: '12-3', name: 'Pink', price: 129.99, inStock: true }
    ]
  }
];

export const productService = {
  getAllProducts: () => Promise.resolve(mockProducts),
  getProductById: (id) => Promise.resolve(mockProducts.find(p => p.id === id)),
  getRelatedProducts: (id, category) => Promise.resolve(mockProducts.filter(p => p.id !== id && p.category === category)),
  getProductsByCategory: (category) => Promise.resolve(category === 'all' ? mockProducts : mockProducts.filter(p => p.category === category)),
  searchProducts: (query) => Promise.resolve(
    mockProducts.filter(p => 
      p.name.toLowerCase().includes(query.toLowerCase()) ||
      p.description.toLowerCase().includes(query.toLowerCase())
    )
  )
};

