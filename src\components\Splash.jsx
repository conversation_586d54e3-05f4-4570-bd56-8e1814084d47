import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  useColorModeValue,
  Image,
  Flex,
  Icon,
  useBreakpointValue,
  ScaleFade,
  SlideFade,
  Progress,
} from '@chakra-ui/react'
import { keyframes } from '@emotion/react'
import { Link as RouterLink, useNavigate } from 'react-router-dom'
import { splashStyles as styles } from '../styles/splash'
import { FiShoppingBag, FiTrendingUp, FiLayers, FiUsers } from 'react-icons/fi'
import '../styles/Splash.css'

function Splash() {
  const [isLoaded, setIsLoaded] = useState(false)
  const [progress, setProgress] = useState(0)
  const navigate = useNavigate()
  const iconSize = useBreakpointValue({ base: '60px', md: '100px', lg: '120px' })

  useEffect(() => {
    // Simulate loading delay for animation effect
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 300)

    // Progress bar animation
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 2
        if (newProgress >= 100) {
          clearInterval(interval)
          // Redirect to home page after progress reaches 100%
          setTimeout(() => navigate('/home'), 500)
        }
        return newProgress > 100 ? 100 : newProgress
      })
    }, 50)

    return () => {
      clearTimeout(timer)
      clearInterval(interval)
    }
  }, [])

  const bgGradient = useColorModeValue(
    'linear(to-b, blue.50, white)',
    'linear(to-b, gray.900, gray.800)'
  )

  const pulse = keyframes`
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  `

  const fadeIn = keyframes`
    from { opacity: 0; }
    to { opacity: 1; }
  `

  const features = [
    { icon: FiShoppingBag, title: 'E-Commerce', description: 'Shop with ease' },
    { icon: FiTrendingUp, title: 'Analytics', description: 'Track your growth' },
    { icon: FiLayers, title: 'Products', description: 'Manage inventory' },
    { icon: FiUsers, title: 'Customers', description: 'Build relationships' },
  ]

  return (
    <Box
      bgGradient={bgGradient}
      {...styles.mainBox}
      className="splash-container"
    >
      <Container {...styles.container}>
        <VStack {...styles.content} spacing={10}>
          <ScaleFade in={isLoaded} initialScale={0.9}>
            <Box
              position="relative"
              animation={`${pulse} 3s infinite ease-in-out`}
              mb={8}
            >
              <Image
                src="/nexusflow-icon.svg"
                alt="NexusFlow Logo"
                boxSize={iconSize}
                className="app-logo"
                borderRadius="xl"
                boxShadow="xl"
              />
            </Box>
          </ScaleFade>

          <SlideFade in={isLoaded} offsetY="20px" delay={0.2}>
            <Heading {...styles.heading} className="splash-heading">
              Welcome to NexusFlow
            </Heading>
          </SlideFade>

          <SlideFade in={isLoaded} offsetY="20px" delay={0.4}>
            <Text {...styles.description} className="splash-description">
              Your one-stop solution for everything digital. Discover amazing features
              and boost your productivity with our cutting-edge e-commerce platform.
            </Text>
          </SlideFade>

          <SlideFade in={isLoaded} offsetY="20px" delay={0.6}>
            <Flex
              wrap="wrap"
              justify="center"
              gap={6}
              mt={8}
              mb={10}
              animation={`${fadeIn} 1s ease-in-out`}
            >
              {features.map((feature, index) => (
                <Box
                  key={index}
                  bg={useColorModeValue('white', 'gray.800')}
                  p={5}
                  borderRadius="lg"
                  boxShadow="md"
                  textAlign="center"
                  width={{ base: '140px', md: '160px' }}
                  transition="transform 0.3s"
                  _hover={{ transform: 'translateY(-5px)', boxShadow: 'lg' }}
                >
                  <Icon
                    as={feature.icon}
                    w={8}
                    h={8}
                    color="blue.500"
                    mb={3}
                  />
                  <Text fontWeight="bold" fontSize="md" mb={1}>{feature.title}</Text>
                  <Text fontSize="sm" color="gray.500">{feature.description}</Text>
                </Box>
              ))}
            </Flex>
          </SlideFade>

          <SlideFade in={isLoaded} offsetY="20px" delay={0.8}>
            <VStack spacing={4} width="100%">
              <HStack {...styles.buttonBox}>
                <Button
                  as={RouterLink}
                  to="/register"
                  colorScheme="blue"
                  size="lg"
                  mr={4}
                  className="splash-button"
                  boxShadow="md"
                  _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                >
                  Get Started
                </Button>
                <Button
                  as={RouterLink}
                  to="/login"
                  variant="outline"
                  colorScheme="blue"
                  size="lg"
                  className="splash-button"
                  _hover={{ transform: 'translateY(-2px)' }}
                >
                  Sign In
                </Button>
              </HStack>

              <Box width="100%" maxW="400px" mt={6}>
                <Text fontSize="sm" mb={2} textAlign="center">Loading application... {progress}%</Text>
                <Progress value={progress} size="sm" colorScheme="blue" borderRadius="full" />
              </Box>
            </VStack>
          </SlideFade>
        </VStack>
      </Container>
    </Box>
  )
}

export default Splash


