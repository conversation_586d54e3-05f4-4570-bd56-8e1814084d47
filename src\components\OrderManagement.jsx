import {
  Container,
  Heading,
  <PERSON>,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON>ge,
  Button,
  HStack,
  Input,
  Select,
  VStack,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  useDisclosure,
} from '@chakra-ui/react'
import { useState } from 'react'
import { FiEdit2, FiTrash2, FiEye } from 'react-icons/fi'

const OrderManagement = () => {
  const toast = useToast()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [orders] = useState([
    {
      id: '1',
      customer: '<PERSON> Do<PERSON>',
      date: '2024-01-20',
      total: 299.99,
      status: 'pending',
      items: [
        { name: 'Product A', quantity: 2, price: 149.99 }
      ]
    },
    // Add more orders
  ])

  const handleStatusChange = (orderId, newStatus) => {
    toast({
      title: 'Order Updated',
      description: `Order #${orderId} status changed to ${newStatus}`,
      status: 'success',
      duration: 3000,
    })
  }

  const viewOrderDetails = (order) => {
    setSelectedOrder(order)
    onOpen()
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <HStack justify="space-between">
          <Heading size="lg">Order Management</Heading>
          <HStack spacing={4}>
            <Input placeholder="Search orders..." w="300px" />
            <Select w="200px" placeholder="Filter by status">
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
            </Select>
          </HStack>
        </HStack>

        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Order ID</Th>
              <Th>Customer</Th>
              <Th>Date</Th>
              <Th>Total</Th>
              <Th>Status</Th>
              <Th>Actions</Th>
            </Tr>
          </Thead>
          <Tbody>
            {orders.map((order) => (
              <Tr key={order.id}>
                <Td>#{order.id}</Td>
                <Td>{order.customer}</Td>
                <Td>{order.date}</Td>
                <Td>${order.total}</Td>
                <Td>
                  <Badge
                    colorScheme={
                      order.status === 'delivered' ? 'green' :
                      order.status === 'shipped' ? 'blue' :
                      order.status === 'processing' ? 'orange' :
                      'yellow'
                    }
                  >
                    {order.status}
                  </Badge>
                </Td>
                <Td>
                  <HStack spacing={2}>
                    <Button
                      size="sm"
                      leftIcon={<FiEye />}
                      onClick={() => viewOrderDetails(order)}
                    >
                      View
                    </Button>
                    <Button
                      size="sm"
                      leftIcon={<FiEdit2 />}
                      colorScheme="blue"
                    >
                      Edit
                    </Button>
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </VStack>

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Order Details #{selectedOrder?.id}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedOrder && (
              <VStack align="stretch" spacing={4}>
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Item</Th>
                      <Th>Quantity</Th>
                      <Th>Price</Th>
                      <Th>Total</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {selectedOrder.items.map((item, index) => (
                      <Tr key={index}>
                        <Td>{item.name}</Td>
                        <Td>{item.quantity}</Td>
                        <Td>${item.price}</Td>
                        <Td>${item.quantity * item.price}</Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  )
}

export default OrderManagement