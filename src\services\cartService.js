import create from 'zustand';
import { persist } from 'zustand/middleware';

const useCartStore = create(
  persist(
    (set, get) => ({
      items: [],
      totalItems: 0,
      totalAmount: 0,

      addToCart: (product, quantity = 1) => {
        const items = get().items;
        const existingItem = items.find(item => item.id === product.id);

        if (existingItem) {
          const updatedItems = items.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
          set(state => ({
            items: updatedItems,
            totalItems: state.totalItems + quantity,
            totalAmount: state.totalAmount + (product.price * quantity)
          }));
        } else {
          set(state => ({
            items: [...items, { ...product, quantity }],
            totalItems: state.totalItems + quantity,
            totalAmount: state.totalAmount + (product.price * quantity)
          }));
        }
      },

      removeFromCart: (productId) => {
        const items = get().items;
        const item = items.find(item => item.id === productId);
        
        set(state => ({
          items: items.filter(item => item.id !== productId),
          totalItems: state.totalItems - item.quantity,
          totalAmount: state.totalAmount - (item.price * item.quantity)
        }));
      },

      clearCart: () => set({ items: [], totalItems: 0, totalAmount: 0 })
    }),
    {
      name: 'cart-storage'
    }
  )
);

export default useCartStore;