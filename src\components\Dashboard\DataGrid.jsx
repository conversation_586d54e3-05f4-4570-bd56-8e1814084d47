import {
  Table, Thead, Tbody, Tr, Th, Td,
  Menu, MenuButton, MenuList, MenuItem,
  Button, HStack, Icon, Badge, Text, Box,
  useColorModeValue, Checkbox
} from '@chakra-ui/react';
import { FiMoreVertical, FiDownload, FiFilter, FiColumns } from 'react-icons/fi';

const DataGrid = ({ data, columns }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  const exportData = (format) => {
    // Implementation for different export formats
    console.log(`Exporting data in ${format} format`);
  };

  return (
    <Box overflowX="auto" className="enhanced-table">
      <HStack mb={4} justify="space-between">
        <HStack spacing={2}>
          <Menu>
            <MenuButton as={Button} leftIcon={<FiColumns />} size="sm">
              Columns
            </MenuButton>
            <MenuList>
              {columns.map(col => (
                <MenuItem key={col.key}>
                  <Checkbox defaultChecked>{col.label}</Checkbox>
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
          <Button leftIcon={<FiFilter />} size="sm">
            Filters
          </Button>
        </HStack>
        <Menu>
          <MenuButton as={Button} leftIcon={<FiDownload />} size="sm">
            Export
          </MenuButton>
          <MenuList>
            <MenuItem onClick={() => exportData('excel')}>Excel</MenuItem>
            <MenuItem onClick={() => exportData('csv')}>CSV</MenuItem>
            <MenuItem onClick={() => exportData('pdf')}>PDF</MenuItem>
          </MenuList>
        </Menu>
      </HStack>

      <Table variant="simple" borderWidth="1px" borderColor={borderColor}>
        <Thead bg={useColorModeValue('gray.50', 'gray.800')}>
          <Tr>
            {columns.map(col => (
              <Th key={col.key}>{col.label}</Th>
            ))}
            <Th width="50px"></Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.map((row, idx) => (
            <Tr
              key={idx}
              _hover={{ bg: hoverBg }}
              cursor="pointer"
              transition="background-color 0.2s"
            >
              {columns.map(col => (
                <Td key={col.key}>
                  {col.format ? col.format(row[col.key]) : row[col.key]}
                </Td>
              ))}
              <Td>
                <Menu>
                  <MenuButton
                    as={Icon}
                    aria-label="Options"
                    icon={<FiMoreVertical />}
                    cursor="pointer"
                  />
                  <MenuList>
                    <MenuItem>View Details</MenuItem>
                    <MenuItem>Edit</MenuItem>
                    <MenuItem color="red.500">Delete</MenuItem>
                  </MenuList>
                </Menu>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default DataGrid;