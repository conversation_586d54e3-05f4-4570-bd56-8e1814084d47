import {
  Box,
  Container,
  Heading,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Select,
  HStack,
  VStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
} from '@chakra-ui/react'
import { useState } from 'react'

const Reports = () => {
  const [timeRange, setTimeRange] = useState('week')
  const [salesData] = useState({
    total: 124500,
    growth: 23.5,
    topProducts: [
      { name: 'Product A', sales: 1200, revenue: 24000 },
      { name: 'Product B', sales: 950, revenue: 19000 },
      { name: 'Product C', sales: 850, revenue: 17000 },
    ],
  })

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <HStack justify="space-between" mb={6}>
          <Heading size="lg">Sales Reports</Heading>
          <Select 
            w="200px" 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
          </Select>
        </HStack>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <Card>
            <CardBody>
              <Stat>
                <StatLabel>Total Sales</StatLabel>
                <StatNumber>${salesData.total.toLocaleString()}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  {salesData.growth}%
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </SimpleGrid>

        <Card>
          <CardBody>
            <Heading size="md" mb={4}>Top Selling Products</Heading>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Product</Th>
                  <Th isNumeric>Sales</Th>
                  <Th isNumeric>Revenue</Th>
                </Tr>
              </Thead>
              <Tbody>
                {salesData.topProducts.map((product, index) => (
                  <Tr key={index}>
                    <Td>{product.name}</Td>
                    <Td isNumeric>{product.sales}</Td>
                    <Td isNumeric>${product.revenue}</Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  )
}

export default Reports
