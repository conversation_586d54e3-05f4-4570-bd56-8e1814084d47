import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  GridItem,
  Text,
  CircularProgress,
  CircularProgressLabel,
  HStack,
  Icon,
  Tooltip,
  useColorModeValue,
  useBreakpointValue,
  Flex,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem
} from '@chakra-ui/react';
import {
  FiCpu,
  FiHardDrive,
  FiWifi,
  FiUsers,
  FiAlertCircle,
  FiMoreVertical,
  FiRefreshCw
} from 'react-icons/fi';

const MetricCard = ({ title, value, icon, status, details, onRefresh }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const statusColor = {
    healthy: 'green.400',
    warning: 'orange.400',
    critical: 'red.400'
  }[status] || 'gray.400';

  return (
    <Box
      bg={bgColor}
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      position="relative"
    >
      <HStack spacing={4} align="center">
        <Icon as={icon} boxSize={6} color={statusColor} />
        <Box flex={1}>
          <Text fontSize="sm" color="gray.500">{title}</Text>
          <Text fontSize="2xl" fontWeight="bold">{value}%</Text>
        </Box>
        <CircularProgress
          value={value}
          color={statusColor}
          size="50px"
          thickness="8px"
        >
          <CircularProgressLabel>{value}%</CircularProgressLabel>
        </CircularProgress>
      </HStack>
      {details && (
        <Text fontSize="sm" color="gray.500" mt={2}>
          {details}
        </Text>
      )}
      <Button
        size="sm"
        variant="ghost"
        leftIcon={<FiRefreshCw />}
        onClick={onRefresh}
        position="absolute"
        top={2}
        right={2}
        opacity={0}
        _groupHover={{ opacity: 1 }}
      >
        Refresh
      </Button>
    </Box>
  );
};

const PerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    cpu: { value: 45, status: 'healthy', details: 'Normal usage' },
    memory: { value: 68, status: 'warning', details: 'High memory usage' },
    network: { value: 32, status: 'healthy', details: 'Network stable' },
    users: { value: 89, status: 'critical', details: 'Near capacity' }
  });

  const refreshMetric = (key) => {
    // Simulate refresh with random values
    setMetrics(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        value: Math.floor(Math.random() * 100)
      }
    }));
  };

  return (
    <Grid 
      templateColumns={{
        base: '1fr',
        sm: 'repeat(2, 1fr)',
        lg: 'repeat(2, 1fr)'
      }}
      gap={{ base: 3, sm: 4 }}
    >
      {Object.entries(metrics).map(([key, data]) => (
        <GridItem key={key}>
          <MetricCard
            title={`${key.charAt(0).toUpperCase() + key.slice(1)} Usage`}
            value={data.value}
            icon={{
              cpu: FiCpu,
              memory: FiHardDrive,
              network: FiWifi,
              users: FiUsers
            }[key]}
            status={data.status}
            details={data.details}
            onRefresh={() => refreshMetric(key)}
          />
        </GridItem>
      ))}
    </Grid>
  );
};

export default PerformanceMonitor;


