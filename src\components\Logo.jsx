import { Box, Text } from '@chakra-ui/react'

export const Logo = () => (
  <Box
    fontSize={{ base: "xl", md: "2xl" }}
    fontWeight="bold"
    letterSpacing="tight"
  >
    <Text
      as="span"
      bgGradient="linear(to-r, blue.500, teal.400)"
      bgClip="text"
    >
      Nexus
    </Text>
    <Text
      as="span"
      bgGradient="linear(to-r, teal.400, purple.500)"
      bgClip="text"
    >
      Flow
    </Text>
  </Box>
)

export default Logo