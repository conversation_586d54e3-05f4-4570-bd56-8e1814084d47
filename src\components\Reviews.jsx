import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  HStack,
  VStack,
  Text,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Button,
  Avatar,
  Icon,
  Badge,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Progress,
  Grid,
  GridItem,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Textarea,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Tag,
  TagLabel,
  TagCloseButton,
  useToast,
  Spinner,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Image,
} from '@chakra-ui/react';
import {
  FiSearch,
  FiFilter,
  FiStar,
  FiMoreVertical,
  FiFlag,
  FiThumbsUp,
  FiMessageSquare,
  FiTrendingUp,
  FiCalendar,
  FiUpload,
  Fi<PERSON>lertTriangle,
} from 'react-icons/fi';

const ITEMS_PER_PAGE = 5;

const SAMPLE_REVIEWS = [
  {
    id: 1,
    author: '<PERSON>',
    avatar: 'https://bit.ly/sage-adebayo',
    rating: 5,
    comment: 'Exceptional quality and service! The Premium Headphones exceeded my expectations in every way. The noise cancellation is outstanding, and the battery life is impressive. Would definitely recommend to anyone looking for high-end audio equipment.',
    date: '2024-01-15T10:30:00',
    verified: true,
    likes: 24,
    tags: ['Quality Audio', 'Long Battery Life'],
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=200',
      'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=200'
    ],
    replies: [
      {
        id: 101,
        author: 'Store Support',
        avatar: 'https://bit.ly/store-support',
        comment: 'Thank you for your wonderful review, Emma! We\'re thrilled to hear about your positive experience.',
        date: '2024-01-15T11:00:00'
      }
    ]
  },
  {
    id: 2,
    author: 'Michael Chen',
    avatar: 'https://bit.ly/ryan-florence',
    rating: 4,
    comment: 'Great product overall. The Smart Watch has excellent features and good build quality. The only minor issue is that the app could be more intuitive. Battery life is fantastic though!',
    date: '2024-01-14T15:45:00',
    verified: true,
    likes: 18,
    tags: ['Smart Features', 'Good Battery'],
    images: [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=200'
    ],
    replies: []
  },
  {
    id: 3,
    author: 'Sarah Johnson',
    avatar: 'https://bit.ly/prosper-baba',
    rating: 3,
    comment: 'The Wireless Earbuds are decent but not perfect. Sound quality is good, but I experienced some connectivity issues. Customer service was helpful in resolving the problems.',
    date: '2024-01-13T09:20:00',
    verified: true,
    likes: 7,
    tags: ['Sound Quality', 'Connectivity'],
    replies: [
      {
        id: 102,
        author: 'Tech Support',
        avatar: 'https://bit.ly/tech-support',
        comment: 'Hi Sarah, thanks for your feedback. We\'ve released a firmware update that addresses the connectivity issues you mentioned.',
        date: '2024-01-13T10:15:00',
      }
    ]
  },
  {
    id: 4,
    author: 'David Brown',
    avatar: 'https://bit.ly/dan-abramov',
    rating: 5,
    comment: 'The Gaming Mouse is absolutely perfect! The precision is incredible, and the customizable buttons have improved my gaming experience significantly. RGB lighting is stunning too.',
    date: '2024-01-12T14:30:00',
    verified: true,
    likes: 32,
    tags: ['Gaming', 'Customization'],
    images: [
      'https://images.unsplash.com/photo-1527814050087-3793815479db?w=200',
    ],
    replies: []
  },
  {
    id: 5,
    author: 'Lisa Anderson',
    avatar: 'https://bit.ly/kent-c-dodds',
    rating: 2,
    comment: 'Disappointed with the Bluetooth Speaker. The sound is okay, but it arrived with some scratches and the battery drains quite quickly. Not worth the price.',
    date: '2024-01-11T16:15:00',
    verified: true,
    likes: 4,
    tags: ['Audio Equipment'],
    replies: [
      {
        id: 103,
        author: 'Customer Service',
        avatar: 'https://bit.ly/customer-service',
        comment: 'We apologize for your experience, Lisa. Please contact our support team for a replacement unit.',
        date: '2024-01-11T17:00:00',
      }
    ]
  },
  {
    id: 6,
    author: 'James Wilson',
    avatar: 'https://bit.ly/code-beast',
    rating: 5,
    comment: 'The 4K Monitor is stunning! Colors are vibrant, refresh rate is smooth, and the build quality is top-notch. Perfect for both gaming and professional work.',
    date: '2024-01-10T11:45:00',
    verified: true,
    likes: 28,
    tags: ['Display Quality', 'Gaming', 'Professional Use'],
    images: [
      'https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=200',
    ],
    replies: []
  },
  {
    id: 7,
    author: 'Nina Patel',
    avatar: 'https://bit.ly/sage-adebayo',
    rating: 4,
    comment: 'Really happy with my Mechanical Keyboard. The typing experience is fantastic and the build quality is solid. Would be perfect if it came with a wrist rest.',
    date: '2024-01-09T13:20:00',
    verified: true,
    likes: 15,
    tags: ['Typing Experience', 'Build Quality'],
    replies: []
  },
];

const ReviewCard = ({ review, onReply, onFlag, onLike, onModerate }) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      p={4}
      bg={cardBg}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack justify="space-between" mb={3}>
        <HStack spacing={3}>
          <Avatar size="md" name={review.author} src={review.avatar} />
          <Box>
            <Text fontWeight="medium">{review.author}</Text>
            <Text fontSize="sm" color="gray.500">
              {review.date}
            </Text>
          </Box>
        </HStack>
        <Menu>
          <MenuButton
            as={Button}
            variant="ghost"
            size="sm"
            rightIcon={<FiMoreVertical />}
          >
            Actions
          </MenuButton>
          <MenuList>
            <MenuItem onClick={() => onReply(review)}>Reply</MenuItem>
            <MenuItem onClick={() => onFlag(review)}>Flag Review</MenuItem>
            <MenuItem onClick={() => onModerate(review)}>Moderate Review</MenuItem>
          </MenuList>
        </Menu>
      </HStack>

      <HStack spacing={1} mb={3}>
        {[...Array(5)].map((_, i) => (
          <Icon
            key={i}
            as={FiStar}
            color={i < review.rating ? "yellow.400" : "gray.300"}
            boxSize={4}
          />
        ))}
        <Badge ml={2} colorScheme={review.verified ? "green" : "gray"}>
          {review.verified ? "Verified Purchase" : "Unverified"}
        </Badge>
      </HStack>

      {review.tags && (
        <HStack spacing={2} mb={3}>
          {review.tags.map((tag, index) => (
            <Tag
              key={index}
              size="sm"
              borderRadius="full"
              variant="subtle"
              colorScheme="blue"
            >
              <TagLabel>{tag}</TagLabel>
            </Tag>
          ))}
        </HStack>
      )}

      <Text mb={4}>{review.comment}</Text>

      {review.images && (
        <HStack spacing={2} mb={4}>
          {review.images.map((image, index) => (
            <Box
              key={index}
              w="60px"
              h="60px"
              borderRadius="md"
              overflow="hidden"
              cursor="pointer"
            >
              <img src={image} alt={`Review ${index + 1}`} style={{ objectFit: 'cover', width: '100%', height: '100%' }} />
            </Box>
          ))}
        </HStack>
      )}

      <HStack spacing={4}>
        <Button
          size="sm"
          leftIcon={<FiThumbsUp />}
          variant="ghost"
          onClick={() => onLike(review)}
        >
          Helpful ({review.likes})
        </Button>
        <Button
          size="sm"
          leftIcon={<FiMessageSquare />}
          variant="ghost"
          onClick={() => onReply(review)}
        >
          Reply ({review.replies?.length || 0})
        </Button>
      </HStack>

      {review.replies && review.replies.length > 0 && (
        <VStack spacing={3} mt={4} pl={6} borderLeftWidth="2px" borderColor="gray.200">
          {review.replies.map((reply, index) => (
            <Box key={index} w="full">
              <HStack mb={1}>
                <Avatar size="sm" name={reply.author} src={reply.avatar} />
                <Text fontWeight="medium">{reply.author}</Text>
                <Text fontSize="sm" color="gray.500">
                  {reply.date}
                </Text>
              </HStack>
              <Text fontSize="sm">{reply.comment}</Text>
            </Box>
          ))}
        </VStack>
      )}
    </Box>
  );
};

const Reviews = () => {
  const [filterRating, setFilterRating] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [reviews, setReviews] = useState(SAMPLE_REVIEWS);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedImage, setSelectedImage] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isModeratingReview, setIsModeratingReview] = useState(false);
  const [reviewToModerate, setReviewToModerate] = useState(null);
  
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedReview, setSelectedReview] = useState(null);
  const [replyText, setReplyText] = useState('');
  const toast = useToast();
  const cancelRef = React.useRef();

  useEffect(() => {
    fetchReviews();
  }, [currentPage, filterRating, sortBy, searchQuery]);

  const fetchReviews = async () => {
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      let filteredReviews = SAMPLE_REVIEWS.filter(review => 
        review.comment.toLowerCase().includes(searchQuery.toLowerCase()) ||
        review.author.toLowerCase().includes(searchQuery.toLowerCase())
      );

      if (filterRating !== 'all') {
        filteredReviews = filteredReviews.filter(review => 
          review.rating === parseInt(filterRating)
        );
      }

      filteredReviews.sort((a, b) => {
        if (sortBy === 'recent') {
          return new Date(b.date) - new Date(a.date);
        }
        return b.likes - a.likes;
      });

      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
      const paginatedReviews = filteredReviews.slice(
        startIndex,
        startIndex + ITEMS_PER_PAGE
      );

      setReviews(paginatedReviews);
      setTotalPages(Math.ceil(filteredReviews.length / ITEMS_PER_PAGE));
    } catch (error) {
      toast({
        title: 'Error fetching reviews',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setIsUploading(true);
      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSelectedImage(URL.createObjectURL(file));
      toast({
        title: 'Image uploaded successfully',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleModerateReview = (review) => {
    setReviewToModerate(review);
    setIsModeratingReview(true);
  };

  const confirmModeration = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setReviews(prev => prev.filter(r => r.id !== reviewToModerate.id));
      toast({
        title: 'Review moderated successfully',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Moderation failed',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsModeratingReview(false);
      setReviewToModerate(null);
    }
  };

  const handleReply = (review) => {
    setSelectedReview(review);
    onOpen();
  };

  const handleFlag = (review) => {
    // Implement flag functionality
    console.log('Flagged review:', review);
  };

  const handleLike = (review) => {
    // Implement like functionality
    console.log('Liked review:', review);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <HStack spacing={4} mb={6}>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <FiSearch color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder="Search reviews..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </InputGroup>
          <Select
            value={filterRating}
            onChange={(e) => setFilterRating(e.target.value)}
            w="150px"
          >
            <option value="all">All Ratings</option>
            {[5, 4, 3, 2, 1].map(rating => (
              <option key={rating} value={rating}>{rating} Stars</option>
            ))}
          </Select>
          <Select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            w="150px"
          >
            <option value="recent">Most Recent</option>
            <option value="helpful">Most Helpful</option>
          </Select>
        </HStack>

        {isLoading ? (
          <VStack py={10}>
            <Spinner size="xl" />
            <Text>Loading reviews...</Text>
          </VStack>
        ) : (
          <VStack spacing={4} align="stretch">
            {reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onReply={handleReply}
                onFlag={handleFlag}
                onLike={handleLike}
                onModerate={handleModerateReview}
              />
            ))}
          </VStack>
        )}

        {/* Pagination */}
        <HStack justify="center" spacing={2} mt={6}>
          {[...Array(totalPages)].map((_, i) => (
            <Button
              key={i}
              size="sm"
              variant={currentPage === i + 1 ? "solid" : "outline"}
              onClick={() => setCurrentPage(i + 1)}
            >
              {i + 1}
            </Button>
          ))}
        </HStack>

        {/* Reply Modal */}
        <Modal isOpen={isOpen} onClose={onClose}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Reply to Review</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <VStack spacing={4}>
                <Textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="Type your reply..."
                  rows={4}
                />
                <Button
                  leftIcon={<FiUpload />}
                  onClick={() => document.getElementById('image-upload').click()}
                  isLoading={isUploading}
                >
                  Upload Image
                </Button>
                <input
                  id="image-upload"
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleImageUpload}
                />
                {selectedImage && (
                  <Image src={selectedImage} maxH="200px" objectFit="cover" />
                )}
              </VStack>
              <Button colorScheme="blue" mt={4} w="full">
                Submit Reply
              </Button>
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Moderation Dialog */}
        <AlertDialog
          isOpen={isModeratingReview}
          leastDestructiveRef={cancelRef}
          onClose={() => setIsModeratingReview(false)}
        >
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader>
                Moderate Review
              </AlertDialogHeader>
              <AlertDialogBody>
                Are you sure you want to moderate this review? This action cannot be undone.
              </AlertDialogBody>
              <AlertDialogFooter>
                <Button ref={cancelRef} onClick={() => setIsModeratingReview(false)}>
                  Cancel
                </Button>
                <Button colorScheme="red" onClick={confirmModeration} ml={3}>
                  Confirm
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </VStack>
    </Container>
  );
};

export default Reviews;



