import {
  Container,
  SimpleGrid,
  Box,
  Heading,
  Text,
  Icon,
  VStack,
  Button,
  useColorModeValue,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import {
  FiCode,
  FiDatabase,
  FiCloud,
  FiShield,
  FiSmartphone,
  FiTrendingUp,
} from 'react-icons/fi';

const services = [
  {
    icon: FiCode,
    title: 'Custom Development',
    description: 'Tailored software solutions built to meet your specific business needs.',
  },
  {
    icon: FiDatabase,
    title: 'Data Analytics',
    description: 'Transform your raw data into actionable insights and strategic decisions.',
  },
  {
    icon: FiCloud,
    title: 'Cloud Solutions',
    description: 'Secure and scalable cloud infrastructure for your growing business.',
  },
  {
    icon: FiShield,
    title: 'Cybersecurity',
    description: 'Protect your digital assets with our advanced security solutions.',
  },
  {
    icon: FiSmartphone,
    title: 'Mobile Development',
    description: 'Create engaging mobile experiences for iOS and Android platforms.',
  },
  {
    icon: FiTrendingUp,
    title: 'Digital Strategy',
    description: 'Strategic consulting to drive your digital transformation journey.',
  },
];

const ServiceCard = ({ title, description, icon }) => {
  const cardBg = useColorModeValue('white', 'gray.700');
  const hoverBg = useColorModeValue('blue.50', 'gray.600');

  return (
    <Box
      p={6}
      bg={cardBg}
      borderRadius="lg"
      boxShadow="md"
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-5px)',
        bg: hoverBg,
        boxShadow: 'lg',
      }}
    >
      <VStack spacing={4} align="start">
        <Icon as={icon} boxSize={8} color="blue.500" />
        <Heading size="md">{title}</Heading>
        <Text color="gray.500">{description}</Text>
        <Button
          as={RouterLink}
          to={`/services#${title.toLowerCase().replace(/\s+/g, '-')}`}
          colorScheme="blue"
          size="sm"
          variant="outline"
        >
          Learn More
        </Button>
      </VStack>
    </Box>
  );
};

function Services() {
  return (
    <Container maxW="container.xl" py={12}>
      <VStack spacing={12}>
        <Box textAlign="center">
          <Heading mb={4}>Our Services</Heading>
          <Text color="gray.500" fontSize="lg">
            Comprehensive solutions to power your business growth
          </Text>
        </Box>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8} width="100%">
          {services.map((service, index) => (
            <ServiceCard key={index} {...service} />
          ))}
        </SimpleGrid>

        <Box textAlign="center" py={8}>
          <Heading size="md" mb={4}>
            Need a custom solution?
          </Heading>
          <Text color="gray.500" mb={6}>
            Let's discuss how we can help your business succeed
          </Text>
          <Button
            as={RouterLink}
            to="/help"
            colorScheme="blue"
            size="lg"
          >
            Contact Us
          </Button>
        </Box>
      </VStack>
    </Container>
  );
}

export default Services;