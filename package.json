{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^16.6.0", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "formik": "^2.4.6", "framer-motion": "^11.18.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-lottie": "^1.2.10", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "yup": "^1.6.1", "zustand": "^4.5.6"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}