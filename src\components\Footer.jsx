import React from 'react';
import {
  Box,
  Container,
  Stack,
  SimpleGrid,
  Text,
  Link,
  useColorModeValue,
  Image,
  HStack,
  Icon,
  Divider
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { FiGithub, FiTwitter, FiInstagram, FiFacebook } from 'react-icons/fi';

const Footer = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      bg={bgColor}
      color={textColor}
      borderTop="1px"
      borderColor={borderColor}
      mt={10}
    >
      <Container as={Stack} maxW="container.xl" py={10}>
        <SimpleGrid
          templateColumns={{ sm: '1fr 1fr', md: '2fr 1fr 1fr 1fr' }}
          spacing={8}
        >
          <Stack spacing={6}>
            <HStack spacing={3}>
              <Box
                bg="blue.500"
                p={2}
                rounded="md"
                width="40px"
                height="40px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Image
                  src="/nexusflow-icon.svg"
                  alt="NexusFlow Logo"
                  width="24px"
                  height="24px"
                />
              </Box>
              <Text
                fontSize="xl"
                fontWeight="bold"
                bgGradient="linear(to-r, blue.500, teal.500)"
                bgClip="text"
              >
                NexusFlow
              </Text>
            </HStack>
            <Text fontSize="sm">
              Your one-stop solution for everything digital. Discover amazing products
              and boost your productivity with our cutting-edge platform.
            </Text>
            <HStack spacing={4}>
              <Link href="#" isExternal>
                <Icon as={FiGithub} w={5} h={5} />
              </Link>
              <Link href="#" isExternal>
                <Icon as={FiTwitter} w={5} h={5} />
              </Link>
              <Link href="#" isExternal>
                <Icon as={FiFacebook} w={5} h={5} />
              </Link>
              <Link href="#" isExternal>
                <Icon as={FiInstagram} w={5} h={5} />
              </Link>
            </HStack>
          </Stack>
          <Stack align="flex-start">
            <Text fontWeight="600" fontSize="lg" mb={2}>
              Shop
            </Text>
            <Link as={RouterLink} to="/products">All Products</Link>
            <Link as={RouterLink} to="/services">Services</Link>
            <Link as={RouterLink} to="/cart">Cart</Link>
            <Link as={RouterLink} to="/checkout">Checkout</Link>
          </Stack>
          <Stack align="flex-start">
            <Text fontWeight="600" fontSize="lg" mb={2}>
              Account
            </Text>
            <Link as={RouterLink} to="/login">Sign In</Link>
            <Link as={RouterLink} to="/register">Sign Up</Link>
            <Link as={RouterLink} to="/profile">My Profile</Link>
            <Link as={RouterLink} to="/orders">My Orders</Link>
          </Stack>
          <Stack align="flex-start">
            <Text fontWeight="600" fontSize="lg" mb={2}>
              Support
            </Text>
            <Link as={RouterLink} to="/help">Help Center</Link>
            <Link href="#">Privacy Policy</Link>
            <Link href="#">Terms of Service</Link>
            <Link href="#">Contact Us</Link>
          </Stack>
        </SimpleGrid>
      </Container>
      <Divider borderColor={borderColor} />
      <Box py={4}>
        <Container maxW="container.xl">
          <Text textAlign="center">
            © {new Date().getFullYear()} NexusFlow. All rights reserved.
          </Text>
        </Container>
      </Box>
    </Box>
  );
};

export default Footer;
