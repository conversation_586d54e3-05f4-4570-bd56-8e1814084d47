import React from 'react';
import {
  Box,
  Flex,
  HStack,
  Link,
  IconButton,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  useDisclosure,
  useColorMode,
  useColorModeValue,
  Stack,
  Icon,
  Avatar,
  Image,
  Text,
  Container,
  Drawer,
  DrawerBody,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  VStack,
  Circle,
} from '@chakra-ui/react'
import { Link as RouterLink, useLocation } from 'react-router-dom'
import { FiMenu, FiX, FiUser, FiSettings, FiLogOut, FiHelpCircle, FiShoppingCart, FiHome, FiPackage, FiClipboard, FiServer } from 'react-icons/fi'
import useAuthStore from '../store/authStore'
import { useCart } from '../context/CartContext'

// Styles
import '../styles/Navbar.css';

function Navbar() {
  const { isOpen, onOpen, onClose } = useDisclosure()
  const { colorMode, toggleColorMode } = useColorMode()
  const { isAuthenticated, user, logout } = useAuthStore()
  const { cart } = useCart()
  const location = useLocation()

  const bgColor = useColorModeValue('white', 'dark.secondary')
  const borderColor = useColorModeValue('gray.200', 'dark.border')
  const textColor = useColorModeValue('gray.800', 'white')

  const NavLink = ({ to, children, icon }) => {
    const isActive = location.pathname === to
    return (
      <Link
        as={RouterLink}
        to={to}
        px={3}
        py={2}
        rounded={'md'}
        _hover={{
          textDecoration: 'none',
          bg: useColorModeValue('gray.100', 'gray.700'),
        }}
        bg={isActive ? useColorModeValue('gray.100', 'gray.700') : 'transparent'}
        color={isActive ? 'blue.500' : textColor}
        display="flex"
        alignItems="center"
        gap={2}
      >
        {icon && <Icon as={icon} />}
        <Text>{children}</Text>
      </Link>
    )
  }

  const MobileNavItem = ({ icon, children, href, onClose }) => {
    return (
      <Link
        as={RouterLink}
        to={href}
        py={3}
        display="flex"
        alignItems="center"
        gap={3}
        px={4}
        _hover={{
          textDecoration: 'none',
          bg: useColorModeValue('gray.100', 'gray.700'),
        }}
        onClick={onClose}
      >
        <Icon as={icon} />
        <Text>{children}</Text>
      </Link>
    )
  }

  return (
    <Box
      as="nav"
      position="sticky"
      top="0"
      zIndex="sticky"
      bg={bgColor}
      borderBottom="1px"
      borderColor={borderColor}
      backdropFilter="blur(10px)"
      transition="all 0.2s"
    >
      <Container maxW="container.xl">
        <Flex h={16} alignItems={'center'} justifyContent={'space-between'}>
          <IconButton
            size={'md'}
            icon={isOpen ? <FiX /> : <FiMenu />}
            aria-label={'Open Menu'}
            display={{ md: 'none' }}
            onClick={isOpen ? onClose : onOpen}
          />

          <HStack spacing={8} alignItems={'center'}>
            <Link
              as={RouterLink}
              to="/"
              _hover={{ textDecoration: 'none' }}
              title="Go to Splash Page"
            >
              <HStack spacing={3}>
                <Box
                  bg="blue.500"
                  p={2}
                  rounded="md"
                  width="40px"
                  height="40px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Image
                    src="/nexusflow-icon.svg"
                    alt="NexusFlow Logo"
                    width="24px"
                    height="24px"
                  />
                </Box>
                <Text
                  fontSize="xl"
                  fontWeight="bold"
                  bgGradient="linear(to-r, blue.500, teal.500)"
                  bgClip="text"
                  display={{ base: 'none', md: 'block' }}
                >
                  NexusFlow
                </Text>
              </HStack>
            </Link>

            <HStack
              as={'nav'}
              spacing={4}
              display={{ base: 'none', md: 'flex' }}
            >
              <NavLink to="/home" icon={FiHome}>Home</NavLink>
              <NavLink to="/products" icon={FiPackage}>Products</NavLink>
              <NavLink to="/services" icon={FiServer}>Services</NavLink>
              {isAuthenticated && (
                <>
                  <NavLink to="/orders" icon={FiClipboard}>Orders</NavLink>
                  <NavLink to="/dashboard" icon={FiUser}>Dashboard</NavLink>
                </>
              )}
            </HStack>
          </HStack>

          <Flex alignItems={'center'} gap={4}>
            <Box position="relative">
              <IconButton
                size="md"
                variant="ghost"
                icon={<FiShoppingCart />}
                aria-label="Shopping Cart"
                as={RouterLink}
                to="/cart"
              />
              {cart.length > 0 && (
                <Circle
                  size="20px"
                  bg="red.500"
                  color="white"
                  fontSize="xs"
                  fontWeight="bold"
                  position="absolute"
                  top="-2px"
                  right="-2px"
                >
                  {cart.length}
                </Circle>
              )}
            </Box>

            {isAuthenticated ? (
              <Menu>
                <MenuButton
                  as={Button}
                  rounded={'full'}
                  variant={'link'}
                  cursor={'pointer'}
                  minW={0}
                >
                  <Avatar
                    size={'sm'}
                    src={user?.avatar || "/default-avatar.png"}
                  />
                </MenuButton>
                <MenuList>
                  <MenuItem as={RouterLink} to="/profile">
                    <HStack spacing={2}>
                      <Icon as={FiUser} />
                      <Text>Profile</Text>
                    </HStack>
                  </MenuItem>
                  <MenuItem as={RouterLink} to="/settings">
                    <HStack spacing={2}>
                      <Icon as={FiSettings} />
                      <Text>Settings</Text>
                    </HStack>
                  </MenuItem>
                  <MenuItem as={RouterLink} to="/help">
                    <HStack spacing={2}>
                      <Icon as={FiHelpCircle} />
                      <Text>Help Center</Text>
                    </HStack>
                  </MenuItem>
                  <MenuDivider />
                  <MenuItem onClick={logout} color="red.500">
                    <HStack spacing={2}>
                      <Icon as={FiLogOut} />
                      <Text>Logout</Text>
                    </HStack>
                  </MenuItem>
                </MenuList>
              </Menu>
            ) : (
              <HStack spacing={2}>
                <Button
                  as={RouterLink}
                  to="/login"
                  variant={'ghost'}
                  size={{ base: 'sm', md: 'md' }}
                >
                  Sign In
                </Button>
                <Button
                  as={RouterLink}
                  to="/register"
                  colorScheme={'blue'}
                  size={{ base: 'sm', md: 'md' }}
                >
                  Sign Up
                </Button>
              </HStack>
            )}
          </Flex>
        </Flex>

        <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton />
            <DrawerHeader>
              <HStack spacing={3}>
                <Image src="/nexusflow-icon.svg" alt="NexusFlow Logo" width="24px" height="24px" />
                <Text
                  fontSize="xl"
                  fontWeight="bold"
                  bgGradient="linear(to-r, blue.500, teal.500)"
                  bgClip="text"
                >
                  NexusFlow
                </Text>
              </HStack>
            </DrawerHeader>

            <DrawerBody>
              <VStack align="stretch" spacing={4}>
                <MobileNavItem href="/home" icon={FiHome} onClose={onClose}>
                  Home
                </MobileNavItem>
                <MobileNavItem href="/products" icon={FiPackage} onClose={onClose}>
                  Products
                </MobileNavItem>
                <MobileNavItem href="/services" icon={FiServer} onClose={onClose}>
                  Services
                </MobileNavItem>
                <MobileNavItem href="/cart" icon={FiShoppingCart} onClose={onClose}>
                  Cart {cart.length > 0 && `(${cart.length})`}
                </MobileNavItem>
                {isAuthenticated && (
                  <>
                    <MobileNavItem href="/orders" icon={FiClipboard} onClose={onClose}>
                      Orders
                    </MobileNavItem>
                    <MobileNavItem href="/dashboard" icon={FiUser} onClose={onClose}>
                      Dashboard
                    </MobileNavItem>
                  </>
                )}

                <Box pt={4}>
                  <Text fontSize="sm" color="gray.500" mb={2}>
                    Account
                  </Text>
                  {isAuthenticated ? (
                    <VStack align="stretch" spacing={1}>
                      <MobileNavItem href="/profile" icon={FiUser} onClose={onClose}>
                        Profile
                      </MobileNavItem>
                      <MobileNavItem href="/settings" icon={FiSettings} onClose={onClose}>
                        Settings
                      </MobileNavItem>
                      <MobileNavItem href="/help" icon={FiHelpCircle} onClose={onClose}>
                        Help Center
                      </MobileNavItem>
                      <Box pt={2}>
                        <Button
                          leftIcon={<FiLogOut />}
                          colorScheme="red"
                          variant="ghost"
                          width="full"
                          justifyContent="start"
                          onClick={() => {
                            logout();
                            onClose();
                          }}
                        >
                          Logout
                        </Button>
                      </Box>
                    </VStack>
                  ) : (
                    <VStack align="stretch" spacing={2}>
                      <Button as={RouterLink} to="/login" variant="outline" width="full">
                        Sign In
                      </Button>
                      <Button as={RouterLink} to="/register" colorScheme="blue" width="full">
                        Sign Up
                      </Button>
                    </VStack>
                  )}
                </Box>
              </VStack>
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      </Container>
    </Box>
  )
}

export default Navbar


