import { ChakraProvider } from '@chakra-ui/react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import AIAssistant from './components/AIAssistant';
import Home from './components/Home';
import Splash from './components/Splash';
import Login from './components/Login';
import Register from './components/Register';
import Products from './components/Products';
import ProductDetails from './components/ProductDetails';
import Services from './components/Services';
import Dashboard from './components/Dashboard';
import MinimalDashboard from './components/Dashboard/otherfeatures';
import UserProfile from './components/UserProfile';
import Settings from './components/Settings';
import OrderManagement from './components/OrderManagement';
import Help from './components/Help';
import Cart from './components/Cart';
import Checkout from './components/Checkout';
import { CartProvider } from './context/CartContext';
import PrivateRoute from './components/PrivateRoute';
import NotFound from './components/NotFound';
import theme from './theme';

function App() {
  return (
    <ChakraProvider theme={theme}>
      <Router>
        <ErrorBoundary>
          <CartProvider>
            <Navbar />
            <AIAssistant />
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Splash />} />
              <Route path="/home" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/products" element={<Products />} />
              <Route path="/product/:id" element={<ProductDetails />} />
              <Route path="/services" element={<Services />} />
              <Route path="/cart" element={<Cart />} />
              <Route path="/checkout" element={<Checkout />} />
              <Route path="/help" element={<Help />} />

              {/* Dashboard Routes - Temporarily unprotected for development */}
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/minimal-dashboard" element={<MinimalDashboard />} />

              {/* Other Protected Routes */}
              <Route element={<PrivateRoute />}>
                <Route path="/profile" element={<UserProfile />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/orders" element={<OrderManagement />} />
              </Route>

              {/* 404 Route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            <Footer />
          </CartProvider>
        </ErrorBoundary>
      </Router>
    </ChakraProvider>
  );
}

export default App;

