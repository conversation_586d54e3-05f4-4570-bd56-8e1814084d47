import {
  Box, SimpleGrid, Text, CircularProgress,
  CircularProgressLabel, useColorModeValue,
  Tooltip, VStack
} from '@chakra-ui/react';

const SystemHealth = () => {
  const metrics = [
    {
      name: 'CPU Usage',
      value: 45,
      color: 'green',
      details: 'Current CPU utilization across all servers'
    },
    {
      name: 'Memory',
      value: 72,
      color: 'orange',
      details: 'RAM usage across the system'
    },
    {
      name: 'Storage',
      value: 60,
      color: 'blue',
      details: 'Available storage capacity'
    },
    {
      name: 'Network',
      value: 85,
      color: 'purple',
      details: 'Network bandwidth utilization'
    }
  ];

  return (
    <Box
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="lg"
      p={4}
      boxShadow="sm"
    >
      <SimpleGrid columns={2} spacing={4}>
        {metrics.map((metric) => (
          <Tooltip key={metric.name} label={metric.details} hasArrow>
            <VStack>
              <CircularProgress
                value={metric.value}
                color={`${metric.color}.400`}
                size="100px"
                thickness="8px"
              >
                <CircularProgressLabel>{metric.value}%</CircularProgressLabel>
              </CircularProgress>
              <Text fontSize="sm" fontWeight="medium">
                {metric.name}
              </Text>
            </VStack>
          </Tooltip>
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default SystemHealth;
