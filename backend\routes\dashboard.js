import express from 'express';
import mongoose from 'mongoose';
import { protect, authorize } from '../middleware/auth.js';
import User from '../models/User.js';
import Product from '../models/Product.js';
import Order from '../models/Order.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// @desc    Get dashboard analytics
// @route   GET /api/dashboard/analytics
// @access  Private/Admin
router.get('/analytics', protect, authorize('admin', 'moderator'), async (req, res) => {
  try {
    const { period = 'month' } = req.query;
    
    // Calculate date range based on period
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Aggregate data
    const [
      totalUsers,
      newUsers,
      totalOrders,
      newOrders,
      totalRevenue,
      revenueData,
      orderStatusData,
      topProducts,
      userGrowth
    ] = await Promise.all([
      // Total users
      User.countDocuments({ isActive: true }),
      
      // New users in period
      User.countDocuments({ 
        createdAt: { $gte: startDate },
        isActive: true 
      }),
      
      // Total orders
      Order.countDocuments(),
      
      // New orders in period
      Order.countDocuments({ 
        createdAt: { $gte: startDate } 
      }),
      
      // Total revenue
      Order.aggregate([
        { $match: { 'payment.status': 'completed' } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      
      // Revenue data for chart
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            'payment.status': 'completed'
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: period === 'week' ? '%Y-%m-%d' : '%Y-%m',
                date: '$createdAt'
              }
            },
            revenue: { $sum: '$pricing.total' },
            orders: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]),
      
      // Order status distribution
      Order.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]),
      
      // Top selling products
      Order.aggregate([
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.product',
            totalSold: { $sum: '$items.quantity' },
            revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: '_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        { $sort: { totalSold: -1 } },
        { $limit: 10 }
      ]),
      
      // User growth data
      User.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: period === 'week' ? '%Y-%m-%d' : '%Y-%m',
                date: '$createdAt'
              }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    // Calculate previous period for comparison
    const previousPeriodStart = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()));
    
    const [previousUsers, previousOrders, previousRevenue] = await Promise.all([
      User.countDocuments({ 
        createdAt: { $gte: previousPeriodStart, $lt: startDate },
        isActive: true 
      }),
      Order.countDocuments({ 
        createdAt: { $gte: previousPeriodStart, $lt: startDate } 
      }),
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: previousPeriodStart, $lt: startDate },
            'payment.status': 'completed'
          }
        },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ])
    ]);

    // Calculate percentage changes
    const userGrowthPercent = previousUsers > 0 ? ((newUsers - previousUsers) / previousUsers * 100) : 0;
    const orderGrowthPercent = previousOrders > 0 ? ((newOrders - previousOrders) / previousOrders * 100) : 0;
    const currentRevenue = totalRevenue[0]?.total || 0;
    const prevRevenue = previousRevenue[0]?.total || 0;
    const revenueGrowthPercent = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue * 100) : 0;

    res.json({
      success: true,
      data: {
        summary: {
          totalUsers,
          newUsers,
          userGrowthPercent: Math.round(userGrowthPercent * 100) / 100,
          totalOrders,
          newOrders,
          orderGrowthPercent: Math.round(orderGrowthPercent * 100) / 100,
          totalRevenue: currentRevenue,
          revenueGrowthPercent: Math.round(revenueGrowthPercent * 100) / 100
        },
        charts: {
          revenue: revenueData,
          userGrowth,
          orderStatus: orderStatusData
        },
        topProducts,
        period,
        dateRange: {
          start: startDate,
          end: now
        }
      }
    });

  } catch (error) {
    logger.error('Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get real-time dashboard data
// @route   GET /api/dashboard/realtime
// @access  Private/Admin
router.get('/realtime', protect, authorize('admin', 'moderator'), async (req, res) => {
  try {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastHour = new Date(now.getTime() - 60 * 60 * 1000);

    const [
      activeUsers,
      recentOrders,
      pendingPayments,
      systemMetrics,
      recentActivity
    ] = await Promise.all([
      // Active users (logged in within last 24 hours)
      User.countDocuments({
        lastLogin: { $gte: last24Hours },
        isActive: true
      }),
      
      // Recent orders (last hour)
      Order.find({
        createdAt: { $gte: lastHour }
      })
      .populate('user', 'firstName lastName email')
      .populate('items.product', 'name images')
      .sort({ createdAt: -1 })
      .limit(10),
      
      // Pending payments
      Order.countDocuments({
        'payment.status': { $in: ['pending', 'processing'] }
      }),
      
      // System metrics (mock data - replace with actual system monitoring)
      Promise.resolve({
        cpu: Math.floor(Math.random() * 100),
        memory: Math.floor(Math.random() * 100),
        disk: Math.floor(Math.random() * 100),
        network: Math.floor(Math.random() * 100)
      }),
      
      // Recent activity
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: last24Hours }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        {
          $project: {
            orderNumber: 1,
            status: 1,
            'pricing.total': 1,
            createdAt: 1,
            'user.firstName': 1,
            'user.lastName': 1,
            'user.email': 1
          }
        },
        { $sort: { createdAt: -1 } },
        { $limit: 20 }
      ])
    ]);

    res.json({
      success: true,
      data: {
        activeUsers,
        recentOrders,
        pendingPayments,
        systemMetrics,
        recentActivity,
        timestamp: now
      }
    });

  } catch (error) {
    logger.error('Real-time dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch real-time data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get performance metrics
// @route   GET /api/dashboard/performance
// @access  Private/Admin
router.get('/performance', protect, authorize('admin', 'moderator'), async (req, res) => {
  try {
    const now = new Date();
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [
      averageOrderValue,
      conversionRate,
      customerRetention,
      inventoryTurnover,
      performanceMetrics
    ] = await Promise.all([
      // Average order value
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: last30Days },
            'payment.status': 'completed'
          }
        },
        {
          $group: {
            _id: null,
            avgOrderValue: { $avg: '$pricing.total' },
            totalOrders: { $sum: 1 }
          }
        }
      ]),
      
      // Conversion rate (orders vs users)
      Promise.all([
        User.countDocuments({ createdAt: { $gte: last30Days } }),
        Order.countDocuments({ createdAt: { $gte: last30Days } })
      ]).then(([users, orders]) => ({
        rate: users > 0 ? (orders / users * 100) : 0,
        users,
        orders
      })),
      
      // Customer retention (repeat customers)
      Order.aggregate([
        {
          $group: {
            _id: '$user',
            orderCount: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: null,
            totalCustomers: { $sum: 1 },
            repeatCustomers: {
              $sum: { $cond: [{ $gt: ['$orderCount', 1] }, 1, 0] }
            }
          }
        }
      ]).then(result => {
        const data = result[0] || { totalCustomers: 0, repeatCustomers: 0 };
        return {
          rate: data.totalCustomers > 0 ? (data.repeatCustomers / data.totalCustomers * 100) : 0,
          ...data
        };
      }),
      
      // Inventory turnover (mock calculation)
      Product.aggregate([
        {
          $group: {
            _id: null,
            totalProducts: { $sum: 1 },
            lowStockProducts: {
              $sum: {
                $cond: [
                  { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] },
                  1,
                  0
                ]
              }
            },
            outOfStockProducts: {
              $sum: {
                $cond: [{ $eq: ['$inventory.quantity', 0] }, 1, 0]
              }
            }
          }
        }
      ]),
      
      // Performance metrics over time
      Order.aggregate([
        {
          $match: {
            createdAt: { $gte: last30Days }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            orders: { $sum: 1 },
            revenue: {
              $sum: {
                $cond: [
                  { $eq: ['$payment.status', 'completed'] },
                  '$pricing.total',
                  0
                ]
              }
            },
            avgOrderValue: { $avg: '$pricing.total' }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    res.json({
      success: true,
      data: {
        metrics: {
          averageOrderValue: averageOrderValue[0]?.avgOrderValue || 0,
          conversionRate: conversionRate.rate,
          customerRetention: customerRetention.rate,
          inventory: inventoryTurnover[0] || {
            totalProducts: 0,
            lowStockProducts: 0,
            outOfStockProducts: 0
          }
        },
        charts: {
          performance: performanceMetrics
        },
        period
      }
    });

  } catch (error) {
    logger.error('Dashboard performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch performance data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get system health
// @route   GET /api/dashboard/health
// @access  Private/Admin
router.get('/health', protect, authorize('admin'), async (req, res) => {
  try {
    // Database health check
    const dbHealth = await mongoose.connection.db.admin().ping();
    
    // Memory usage
    const memoryUsage = process.memoryUsage();
    
    // System uptime
    const uptime = process.uptime();
    
    // Active connections (mock data)
    const activeConnections = Math.floor(Math.random() * 100) + 50;
    
    // Response times (mock data)
    const responseTime = Math.floor(Math.random() * 200) + 50;

    const healthData = {
      status: 'healthy',
      timestamp: new Date(),
      database: {
        status: dbHealth.ok === 1 ? 'connected' : 'disconnected',
        responseTime: Date.now() // This would be actual DB response time
      },
      server: {
        uptime: Math.floor(uptime),
        memory: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
        },
        cpu: Math.floor(Math.random() * 100), // Mock CPU usage
        activeConnections,
        responseTime
      },
      services: {
        mongodb: dbHealth.ok === 1 ? 'operational' : 'down',
        redis: 'operational', // Mock status
        email: 'operational', // Mock status
        payments: {
          stripe: 'operational',
          paypal: 'operational',
          mpesa: 'operational'
        }
      }
    };

    res.json({
      success: true,
      data: healthData
    });

  } catch (error) {
    logger.error('System health check error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system health',
      data: {
        status: 'unhealthy',
        timestamp: new Date(),
        error: process.env.NODE_ENV === 'development' ? error.message : 'System check failed'
      }
    });
  }
});

// @desc    Get live analytics data
// @route   GET /api/dashboard/live
// @access  Private/Admin
router.get('/live', protect, authorize('admin', 'moderator'), async (req, res) => {
  try {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastHour = new Date(now.getTime() - 60 * 60 * 1000);

    // Get hourly data for the last 24 hours
    const hourlyData = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: last24Hours }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%H',
              date: '$createdAt'
            }
          },
          orders: { $sum: 1 },
          revenue: {
            $sum: {
              $cond: [
                { $eq: ['$payment.status', 'completed'] },
                '$pricing.total',
                0
              ]
            }
          },
          visitors: { $addToSet: '$user' }
        }
      },
      {
        $project: {
          hour: '$_id',
          orders: 1,
          revenue: 1,
          visitors: { $size: '$visitors' }
        }
      },
      { $sort: { hour: 1 } }
    ]);

    // Current active metrics
    const currentMetrics = {
      activeUsers: Math.floor(Math.random() * 50) + 10, // Mock active users
      ordersLastHour: await Order.countDocuments({ createdAt: { $gte: lastHour } }),
      revenueLastHour: await Order.aggregate([
        {
          $match: {
            createdAt: { $gte: lastHour },
            'payment.status': 'completed'
          }
        },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]).then(result => result[0]?.total || 0),
      conversionRate: Math.random() * 5 + 1, // Mock conversion rate
      bounceRate: Math.random() * 30 + 20, // Mock bounce rate
      avgSessionDuration: Math.floor(Math.random() * 300) + 120 // Mock session duration in seconds
    };

    res.json({
      success: true,
      data: {
        current: currentMetrics,
        hourly: hourlyData,
        timestamp: now
      }
    });

  } catch (error) {
    logger.error('Live analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch live analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get customer insights
// @route   GET /api/dashboard/customers
// @access  Private/Admin
router.get('/customers', protect, authorize('admin', 'moderator'), async (req, res) => {
  try {
    const now = new Date();
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [
      topCustomers,
      customerSegments,
      customerActivity,
      geographicData
    ] = await Promise.all([
      // Top customers by revenue
      Order.aggregate([
        {
          $match: {
            'payment.status': 'completed'
          }
        },
        {
          $group: {
            _id: '$user',
            totalSpent: { $sum: '$pricing.total' },
            orderCount: { $sum: 1 },
            lastOrder: { $max: '$createdAt' }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        { $sort: { totalSpent: -1 } },
        { $limit: 10 }
      ]),
      
      // Customer segments
      User.aggregate([
        {
          $group: {
            _id: '$role',
            count: { $sum: 1 }
          }
        }
      ]),
      
      // Customer activity over time
      User.aggregate([
        {
          $match: {
            createdAt: { $gte: last30Days }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            newCustomers: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]),
      
      // Geographic distribution (mock data)
      Promise.resolve([
        { country: 'Kenya', customers: 450, percentage: 45 },
        { country: 'Uganda', customers: 230, percentage: 23 },
        { country: 'Tanzania', customers: 180, percentage: 18 },
        { country: 'Rwanda', customers: 90, percentage: 9 },
        { country: 'Others', customers: 50, percentage: 5 }
      ])
    ]);

    res.json({
      success: true,
      data: {
        topCustomers,
        segments: customerSegments,
        activity: customerActivity,
        geographic: geographicData
      }
    });

  } catch (error) {
    logger.error('Customer insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer insights',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
