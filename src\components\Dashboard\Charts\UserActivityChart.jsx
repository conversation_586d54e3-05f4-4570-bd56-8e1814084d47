import { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  useColorModeValue,
  Button,
  Icon,
  Tooltip,
  Badge,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  SimpleGrid
} from '@chakra-ui/react';
import { FiUsers, FiUserPlus, FiUserCheck, FiUserX, FiRefreshCw } from 'react-icons/fi';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';

// Sample data
const generateTimeData = () => {
  const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);
  
  // Generate a pattern that resembles real user activity
  return hours.map(hour => {
    const hourNum = parseInt(hour);
    let value;
    
    // Simulate typical usage patterns throughout the day
    if (hourNum >= 0 && hourNum < 6) {
      // Night hours - low activity
      value = Math.floor(Math.random() * 50) + 10;
    } else if (hourNum >= 6 && hourNum < 9) {
      // Morning ramp-up
      value = Math.floor(Math.random() * 150) + 50;
    } else if (hourNum >= 9 && hourNum < 12) {
      // Morning work hours - high activity
      value = Math.floor(Math.random() * 200) + 150;
    } else if (hourNum >= 12 && hourNum < 14) {
      // Lunch time - medium activity
      value = Math.floor(Math.random() * 100) + 100;
    } else if (hourNum >= 14 && hourNum < 18) {
      // Afternoon work hours - high activity
      value = Math.floor(Math.random() * 200) + 150;
    } else if (hourNum >= 18 && hourNum < 22) {
      // Evening hours - medium to high activity
      value = Math.floor(Math.random() * 150) + 100;
    } else {
      // Late night - declining activity
      value = Math.floor(Math.random() * 100) + 50;
    }
    
    return {
      name: hour,
      users: value
    };
  });
};

const userTypeData = [
  { name: 'New Users', value: 120, color: '#4299E1' },  // blue.400
  { name: 'Returning', value: 280, color: '#48BB78' },  // green.400
  { name: 'Inactive', value: 50, color: '#F56565' }     // red.400
];

const userDeviceData = [
  { name: 'Mobile', value: 210, color: '#9F7AEA' },     // purple.400
  { name: 'Desktop', value: 170, color: '#ED8936' },    // orange.400
  { name: 'Tablet', value: 70, color: '#ECC94B' }       // yellow.400
];

const UserActivityChart = () => {
  const [timeData] = useState(generateTimeData());
  const [activeIndex, setActiveIndex] = useState(null);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const areaColor = useColorModeValue('#4299E1', '#63B3ED'); // blue.400, blue.300
  
  const totalUsers = userTypeData.reduce((sum, item) => sum + item.value, 0);
  const activeUsers = userTypeData[1].value; // Returning users
  
  const onPieEnter = (_, index) => {
    setActiveIndex(index);
  };
  
  const onPieLeave = () => {
    setActiveIndex(null);
  };
  
  const renderActiveShape = (props) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
    
    return (
      <g>
        <text x={cx} y={cy} dy={-20} textAnchor="middle" fill={textColor} fontSize="12px">
          {payload.name}
        </text>
        <text x={cx} y={cy} textAnchor="middle" fill={textColor} fontSize="16px" fontWeight="bold">
          {value}
        </text>
        <text x={cx} y={cy} dy={20} textAnchor="middle" fill={textColor} fontSize="12px">
          {`${(percent * 100).toFixed(0)}%`}
        </text>
        <g>
          <path
            d={`M${cx},${cy}L${cx},${cy}`}
            fill="none"
            stroke={fill}
            strokeWidth={2}
          />
        </g>
      </g>
    );
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">User Activity</Heading>
          <Text color={textColor} fontSize="sm">
            Today's user engagement metrics
          </Text>
        </Box>
        <HStack>
          <Badge colorScheme="green" fontSize="sm" px={2} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={FiUserCheck} />
              <Text>{activeUsers} active</Text>
            </HStack>
          </Badge>
          <Tooltip label="Refresh data">
            <Button size="sm" variant="ghost">
              <Icon as={FiRefreshCw} />
            </Button>
          </Tooltip>
        </HStack>
      </Flex>
      
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} mb={4}>
        <Stat>
          <StatLabel>Total Users</StatLabel>
          <StatNumber>{totalUsers}</StatNumber>
          <StatHelpText>
            <StatArrow type="increase" />
            23.36%
          </StatHelpText>
        </Stat>
        <Stat>
          <StatLabel>New Signups</StatLabel>
          <StatNumber>{userTypeData[0].value}</StatNumber>
          <StatHelpText>
            <StatArrow type="increase" />
            12.05%
          </StatHelpText>
        </Stat>
        <Stat>
          <StatLabel>Avg. Session</StatLabel>
          <StatNumber>8m 42s</StatNumber>
          <StatHelpText>
            <StatArrow type="decrease" />
            3.12%
          </StatHelpText>
        </Stat>
      </SimpleGrid>
      
      <Box height={{ base: '200px', md: '220px' }} mb={4}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={timeData} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke={borderColor} />
            <XAxis 
              dataKey="name" 
              tick={{ fill: textColor, fontSize: 10 }} 
              interval="preserveStartEnd"
              tickFormatter={(value) => value.split(':')[0]}
            />
            <YAxis tick={{ fill: textColor, fontSize: 10 }} />
            <RechartsTooltip 
              contentStyle={{ 
                backgroundColor: bgColor, 
                borderColor: borderColor,
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
              formatter={(value) => [`${value} users`, 'Active Users']}
              labelFormatter={(label) => `Time: ${label}`}
            />
            <Area 
              type="monotone" 
              dataKey="users" 
              stroke={areaColor} 
              fill={areaColor} 
              fillOpacity={0.3} 
              activeDot={{ r: 6 }} 
            />
          </AreaChart>
        </ResponsiveContainer>
      </Box>
      
      <Divider my={4} />
      
      <Heading size="sm" mb={4}>User Segments</Heading>
      
      <Flex 
        direction={{ base: 'column', md: 'row' }} 
        justify="space-between" 
        align="center"
        height={{ base: 'auto', md: '180px' }}
      >
        <Box width={{ base: '100%', md: '50%' }} height={{ base: '180px', md: '100%' }}>
          <Heading size="xs" textAlign="center" mb={2}>User Types</Heading>
          <ResponsiveContainer width="100%" height="90%">
            <PieChart>
              <Pie
                activeIndex={activeIndex === 0 ? [0, 1, 2] : null}
                activeShape={renderActiveShape}
                data={userTypeData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={60}
                dataKey="value"
                onMouseEnter={() => onPieEnter(null, 0)}
                onMouseLeave={onPieLeave}
              >
                {userTypeData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </Box>
        
        <Box width={{ base: '100%', md: '50%' }} height={{ base: '180px', md: '100%' }}>
          <Heading size="xs" textAlign="center" mb={2}>Device Types</Heading>
          <ResponsiveContainer width="100%" height="90%">
            <PieChart>
              <Pie
                activeIndex={activeIndex === 1 ? [0, 1, 2] : null}
                activeShape={renderActiveShape}
                data={userDeviceData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={60}
                dataKey="value"
                onMouseEnter={() => onPieEnter(null, 1)}
                onMouseLeave={onPieLeave}
              >
                {userDeviceData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </Box>
      </Flex>
      
      <Flex justify="center" mt={2}>
        <HStack spacing={4} wrap="wrap" justify="center">
          {[...userTypeData, ...userDeviceData].map((entry, index) => (
            <HStack key={`legend-${index}`} spacing={1}>
              <Box w="10px" h="10px" borderRadius="full" bg={entry.color} />
              <Text fontSize="xs" color={textColor}>{entry.name}</Text>
            </HStack>
          ))}
        </HStack>
      </Flex>
    </Box>
  );
};

export default UserActivityChart;
