import { Box, Spinner, Text, Image } from '@chakra-ui/react'
import '../styles/Loading.css'

function Loading({ message = 'Loading...' }) {
  return (
    <Box className="loading-container">
      <div className="loading-background"></div>
      <div className="loading-content">
        <Image
          src="/nexusflow-icon.svg"
          alt="NexusFlow Logo"
          width="80px"
          height="80px"
          borderRadius="16px"
          className="loading-app-icon"
          boxShadow="0 8px 32px rgba(0, 0, 0, 0.2)"
        />
        <Spinner
          thickness="4px"
          speed="0.65s"
          emptyColor="gray.200"
          color="blue.500"
          size="xl"
          className="loading-spinner"
        />
        <Text className="loading-text">{message}</Text>
      </div>
    </Box>
  )
}

export default Loading
