import React from 'react';
import {
  <PERSON><PERSON>er,
  <PERSON>ing,
  Grid,
  Card,
  CardHeader,
  CardBody,
  Button,
  Text,
  SimpleGrid,
  Box,
  Icon,
  Image,
  Stack,
  HStack,
  VStack,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Divider,
  Avatar,
  AvatarGroup,
  Skeleton,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Progress,
  Tooltip,
  CircularProgress,
  CircularProgressLabel,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
} from '@chakra-ui/react'
import { Link as RouterLink } from 'react-router-dom'
import { useState, useEffect } from 'react'
import {
  FiTrendingUp,
  FiUsers,
  FiAward,
  FiShoppingBag,
  FiActivity,
  FiArrowRight,
  FiStar,
  FiClock,
  FiBell,
  FiPackage,
  FiDollarSign,
  FiPie<PERSON><PERSON>,
} from 'react-icons/fi'

// Styles
import '../styles/Home.css';

// Mock data
const commercials = [
  {
    id: 1,
    title: "Summer Sale",
    description: "Up to 70% off on selected items",
    image: "https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80",
    badge: "Limited Time",
  },
  {
    id: 2,
    title: "New Collection",
    description: "Discover our latest premium products",
    image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80",
    badge: "New Arrival",
  },
  {
    id: 3,
    title: "Premium Membership",
    description: "Join now and get exclusive benefits",
    image: "https://images.unsplash.com/photo-1560769629-975ec94e6a86?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80",
    badge: "Special Offer",
  },
]

const liveStats = [
  { label: "Active Users", value: "1,234", change: 12 },
  { label: "Total Sales", value: "$45.6K", change: 8.5 },
  { label: "Conversion Rate", value: "3.2%", change: -2.1 },
  { label: "New Signups", value: "127", change: 15.8 },
]

const featuredProducts = [
  {
    id: 1,
    name: "Premium Headphones",
    price: "$299",
    rating: 4.8,
    image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    sales: 1234,
  },
  {
    id: 2,
    name: "Smart Watch",
    price: "$199",
    rating: 4.6,
    image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    sales: 956,
  },
  {
    id: 3,
    name: "Wireless Earbuds",
    price: "$159",
    rating: 4.7,
    image: "https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    sales: 847,
  },
]

const trendingCategories = [
  { name: 'Electronics', growth: 25, sales: 1234 },
  { name: 'Fashion', growth: 18, sales: 956 },
  { name: 'Home & Living', growth: 15, sales: 847 },
  { name: 'Books', growth: 12, sales: 654 },
];

const recentTransactions = [
  { id: 1, user: 'John Doe', amount: 299, product: 'Premium Headphones', status: 'completed' },
  { id: 2, user: 'Jane Smith', amount: 199, product: 'Smart Watch', status: 'pending' },
  { id: 3, user: 'Mike Johnson', amount: 159, product: 'Wireless Earbuds', status: 'processing' },
];

const performanceMetrics = [
  { label: 'Revenue Growth', value: 68, color: 'green' },
  { label: 'Customer Satisfaction', value: 92, color: 'blue' },
  { label: 'Market Share', value: 45, color: 'purple' },
];

function Home() {
  const [currentCommercial, setCurrentCommercial] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const cardBg = useColorModeValue('white', 'gray.700')
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [selectedMetric, setSelectedMetric] = useState(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentCommercial((prev) => (prev + 1) % commercials.length)
    }, 5000)

    const loadingTimer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)

    return () => {
      clearInterval(timer)
      clearTimeout(loadingTimer)
    }
  }, [])

  const CommercialBanner = ({ commercial }) => (
    <Card overflow="hidden" position="relative">
      <Image
        src={commercial.image}
        alt={commercial.title}
        height="300px"
        objectFit="cover"
        filter="brightness(0.7)"
      />
      <Box
        position="absolute"
        bottom="0"
        left="0"
        right="0"
        p={6}
        bgGradient="linear(to-t, blackAlpha.800, blackAlpha.300)"
        color="white"
      >
        <Badge colorScheme="red" mb={2}>{commercial.badge}</Badge>
        <Heading size="lg" mb={2}>{commercial.title}</Heading>
        <Text fontSize="lg">{commercial.description}</Text>
      </Box>
    </Card>
  )

  const StatCard = ({ label, value, change }) => (
    <Card bg={cardBg}>
      <CardBody>
        <Stat>
          <StatLabel fontSize="sm" color="gray.500">{label}</StatLabel>
          <StatNumber fontSize="2xl">{value}</StatNumber>
          <StatHelpText>
            <StatArrow type={change > 0 ? 'increase' : 'decrease'} />
            {Math.abs(change)}%
          </StatHelpText>
        </Stat>
      </CardBody>
    </Card>
  )

  const ProductCard = ({ product }) => (
    <Card bg={cardBg} className="product-card">
      <Image
        src={product.image}
        alt={product.name}
        height="200px"
        objectFit="cover"
      />
      <CardBody>
        <VStack align="start" spacing={2}>
          <Heading size="md">{product.name}</Heading>
          <HStack justify="space-between" width="100%">
            <Text fontSize="xl" fontWeight="bold" color="blue.500">
              {product.price}
            </Text>
            <HStack>
              <Icon as={FiStar} color="yellow.400" />
              <Text>{product.rating}</Text>
            </HStack>
          </HStack>
          <Text color="gray.500" fontSize="sm">
            {product.sales.toLocaleString()} units sold
          </Text>
          <Button
            as={RouterLink}
            to={`/product/${product.id}`}
            colorScheme="blue"
            width="100%"
            mt={2}
          >
            View Details
          </Button>
        </VStack>
      </CardBody>
    </Card>
  )

  const handleMetricClick = (metric) => {
    setSelectedMetric(metric)
    onOpen()
  }

  return (
    <Container maxW="container.xl" py={8}>
      {/* Hero Section */}
      <Box textAlign="center" mb={10}>
        <Heading size="2xl" mb={4} className="home-hero">
          Welcome to NexusFlow
        </Heading>
        <Text fontSize="xl" color="gray.500">
          Your one-stop solution for everything digital
        </Text>
      </Box>

      {/* Commercial Banner */}
      <Box mb={10}>
        <Skeleton isLoaded={!isLoading}>
          <CommercialBanner commercial={commercials[currentCommercial]} />
        </Skeleton>
      </Box>

      {/* Live Statistics */}
      <Box mb={10}>
        <Heading size="lg" mb={6}>Live Statistics</Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
          {liveStats.map((stat, index) => (
            <Skeleton key={index} isLoaded={!isLoading}>
              <StatCard {...stat} />
            </Skeleton>
          ))}
        </SimpleGrid>
      </Box>

      {/* Featured Products */}
      <Box mb={10}>
        <HStack justify="space-between" mb={6}>
          <Heading size="lg">Featured Products</Heading>
          <Button
            as={RouterLink}
            to="/products"
            rightIcon={<FiArrowRight />}
            variant="ghost"
            colorScheme="blue"
          >
            View All
          </Button>
        </HStack>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          {featuredProducts.map((product) => (
            <Skeleton key={product.id} isLoaded={!isLoading}>
              <ProductCard product={product} />
            </Skeleton>
          ))}
        </SimpleGrid>
      </Box>

      {/* New Sections */}
      <Box mb={10}>
        <Tabs variant="soft-rounded" colorScheme="blue">
          <TabList>
            <Tab>Trending Categories</Tab>
            <Tab>Recent Transactions</Tab>
            <Tab>Performance Metrics</Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                {trendingCategories.map((category, index) => (
                  <Box
                    key={index}
                    p={5}
                    shadow="md"
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={cardBg}
                  >
                    <HStack justify="space-between" mb={4}>
                      <Text fontSize="lg" fontWeight="bold">{category.name}</Text>
                      <Badge colorScheme="green">+{category.growth}%</Badge>
                    </HStack>
                    <Progress
                      value={category.growth}
                      colorScheme="blue"
                      size="sm"
                      mb={2}
                    />
                    <Text color="gray.500" fontSize="sm">
                      {category.sales} sales this month
                    </Text>
                  </Box>
                ))}
              </SimpleGrid>
            </TabPanel>

            <TabPanel>
              <VStack spacing={4} align="stretch">
                {recentTransactions.map((transaction) => (
                  <Box
                    key={transaction.id}
                    p={4}
                    shadow="sm"
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={cardBg}
                  >
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="bold">{transaction.user}</Text>
                        <Text fontSize="sm" color="gray.500">
                          {transaction.product}
                        </Text>
                      </VStack>
                      <VStack align="end" spacing={1}>
                        <Text fontWeight="bold" color="blue.500">
                          ${transaction.amount}
                        </Text>
                        <Badge
                          colorScheme={
                            transaction.status === 'completed'
                              ? 'green'
                              : transaction.status === 'pending'
                              ? 'yellow'
                              : 'purple'
                          }
                        >
                          {transaction.status}
                        </Badge>
                      </VStack>
                    </HStack>
                  </Box>
                ))}
              </VStack>
            </TabPanel>

            <TabPanel>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                {performanceMetrics.map((metric, index) => (
                  <Box
                    key={index}
                    p={5}
                    shadow="md"
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={cardBg}
                    onClick={() => handleMetricClick(metric)}
                    cursor="pointer"
                    transition="transform 0.2s"
                    _hover={{ transform: 'scale(1.02)' }}
                  >
                    <VStack>
                      <CircularProgress
                        value={metric.value}
                        color={`${metric.color}.400`}
                        size="120px"
                      >
                        <CircularProgressLabel>{metric.value}%</CircularProgressLabel>
                      </CircularProgress>
                      <Text fontWeight="bold">{metric.label}</Text>
                    </VStack>
                  </Box>
                ))}
              </SimpleGrid>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>

      {/* Metric Detail Dialog */}
      <AlertDialog isOpen={isOpen} onClose={onClose}>
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {selectedMetric?.label} Details
            </AlertDialogHeader>

            <AlertDialogBody>
              <VStack align="start" spacing={4}>
                <Text>Current Value: {selectedMetric?.value}%</Text>
                <Text>Previous Period: {(selectedMetric?.value - 5).toFixed(1)}%</Text>
                <Text>Industry Average: {(selectedMetric?.value - 10).toFixed(1)}%</Text>
                <Progress
                  value={selectedMetric?.value}
                  colorScheme={selectedMetric?.color}
                  size="lg"
                  width="100%"
                />
              </VStack>
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button onClick={onClose}>Close</Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* Quick Actions */}
      <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={8}>
        <Card bg={cardBg}>
          <CardHeader>
            <Heading size="lg">Recent Activity</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <HStack justify="space-between">
                <HStack>
                  <Icon as={FiActivity} color="green.500" />
                  <Text>New order received</Text>
                </HStack>
                <Text color="gray.500" fontSize="sm">2m ago</Text>
              </HStack>
              <HStack justify="space-between">
                <HStack>
                  <Icon as={FiUsers} color="blue.500" />
                  <Text>5 new users joined</Text>
                </HStack>
                <Text color="gray.500" fontSize="sm">15m ago</Text>
              </HStack>
              <HStack justify="space-between">
                <HStack>
                  <Icon as={FiAward} color="purple.500" />
                  <Text>Achievement unlocked</Text>
                </HStack>
                <Text color="gray.500" fontSize="sm">1h ago</Text>
              </HStack>
            </VStack>
          </CardBody>
        </Card>

        <Card bg={cardBg}>
          <CardHeader>
            <Heading size="lg">Quick Actions</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={2} spacing={4}>
              <Button
                as={RouterLink}
                to="/dashboard"
                colorScheme="blue"
                leftIcon={<Icon as={FiTrendingUp} />}
              >
                Dashboard
              </Button>
              <Button
                as={RouterLink}
                to="/profile"
                colorScheme="green"
                leftIcon={<Icon as={FiUsers} />}
              >
                Profile
              </Button>
              <Button
                as={RouterLink}
                to="/products"
                colorScheme="purple"
                leftIcon={<Icon as={FiShoppingBag} />}
              >
                Products
              </Button>
              <Button
                as={RouterLink}
                to="/dashboard"
                colorScheme="orange"
                leftIcon={<Icon as={FiClock} />}
              >
                Reports
              </Button>
            </SimpleGrid>
          </CardBody>
        </Card>
      </Grid>
    </Container>
  )
}

export default Home

