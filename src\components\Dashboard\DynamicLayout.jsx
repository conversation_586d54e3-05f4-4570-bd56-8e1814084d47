import { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
  Box,
  Grid,
  GridItem,
  IconButton,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  HStack,
  useBreakpointValue,
  Button,
  Tooltip,
  Text,
  Flex
} from '@chakra-ui/react';
import {
  FiMove,
  FiMaximize2,
  FiMinimize2,
  FiMoreVertical,
  FiSave,
  FiRefreshCw,
  FiLayout,
  FiGrid,
  FiMonitor,
  FiSmartphone
} from 'react-icons/fi';

const DynamicLayout = ({ widgets }) => {
  const [layout, setLayout] = useState(widgets);
  const [expanded, setExpanded] = useState(null);
  const [savedLayouts, setSavedLayouts] = useState({});
  const [currentView, setCurrentView] = useState('default');

  // Responsive settings
  const isMobile = useBreakpointValue({ base: true, md: false });
  const isTablet = useBreakpointValue({ base: false, md: true, lg: false });
  const isDesktop = useBreakpointValue({ base: false, md: false, lg: true });

  // Load saved layout on mount or when view changes
  useEffect(() => {
    const savedLayoutsFromStorage = localStorage.getItem('dashboardLayouts');
    if (savedLayoutsFromStorage) {
      setSavedLayouts(JSON.parse(savedLayoutsFromStorage));

      // If there's a saved layout for the current view, use it
      const parsedLayouts = JSON.parse(savedLayoutsFromStorage);
      if (parsedLayouts[currentView]) {
        setLayout(parsedLayouts[currentView]);
      }
    }
  }, [currentView]);

  // Save current layout
  const saveCurrentLayout = () => {
    const updatedLayouts = {
      ...savedLayouts,
      [currentView]: layout
    };
    setSavedLayouts(updatedLayouts);
    localStorage.setItem('dashboardLayouts', JSON.stringify(updatedLayouts));
  };

  // Reset to default layout
  const resetLayout = () => {
    setLayout(widgets);
    setExpanded(null);
  };

  // Switch between device views
  const switchToView = (view) => {
    setCurrentView(view);
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(layout);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setLayout(items);
  };

  return (
    <Box>
      <Flex justify="space-between" align="center" mb={4}>
        <HStack spacing={2}>
          <Tooltip label="Save current layout">
            <Button size="sm" leftIcon={<FiSave />} onClick={saveCurrentLayout}>
              Save Layout
            </Button>
          </Tooltip>
          <Tooltip label="Reset to default layout">
            <Button size="sm" leftIcon={<FiRefreshCw />} onClick={resetLayout} variant="outline">
              Reset
            </Button>
          </Tooltip>
        </HStack>

        <HStack spacing={2}>
          <Text fontSize="sm" color="gray.500" mr={2}>View as:</Text>
          <Tooltip label="Desktop view">
            <IconButton
              icon={<FiMonitor />}
              size="sm"
              aria-label="Desktop view"
              onClick={() => switchToView('desktop')}
              colorScheme={currentView === 'desktop' ? 'blue' : 'gray'}
              variant={currentView === 'desktop' ? 'solid' : 'outline'}
            />
          </Tooltip>
          <Tooltip label="Tablet view">
            <IconButton
              icon={<FiGrid />}
              size="sm"
              aria-label="Tablet view"
              onClick={() => switchToView('tablet')}
              colorScheme={currentView === 'tablet' ? 'blue' : 'gray'}
              variant={currentView === 'tablet' ? 'solid' : 'outline'}
            />
          </Tooltip>
          <Tooltip label="Mobile view">
            <IconButton
              icon={<FiSmartphone />}
              size="sm"
              aria-label="Mobile view"
              onClick={() => switchToView('mobile')}
              colorScheme={currentView === 'mobile' ? 'blue' : 'gray'}
              variant={currentView === 'mobile' ? 'solid' : 'outline'}
            />
          </Tooltip>
        </HStack>
      </Flex>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="dashboard">
          {(provided) => (
            <Grid
              templateColumns="repeat(12, 1fr)"
              gap={4}
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
            {layout.map((widget, index) => (
              <Draggable
                key={widget.id}
                draggableId={widget.id}
                index={index}
              >
                {(provided, snapshot) => (
                  <GridItem
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    colSpan={expanded === widget.id ? 12 : widget.width}
                    transition="all 0.3s"
                    transform={snapshot.isDragging ? 'scale(1.02)' : ''}
                  >
                    <Box
                      bg={useColorModeValue('white', 'gray.800')}
                      p={4}
                      borderRadius="lg"
                      boxShadow="sm"
                      position="relative"
                      h="full"
                    >
                      <HStack
                        position="absolute"
                        top={2}
                        right={2}
                        opacity={0}
                        transition="opacity 0.2s"
                        _groupHover={{ opacity: 1 }}
                      >
                        <IconButton
                          icon={<FiMove />}
                          variant="ghost"
                          size="sm"
                          {...provided.dragHandleProps}
                        />
                        <IconButton
                          icon={expanded === widget.id ? <FiMinimize2 /> : <FiMaximize2 />}
                          variant="ghost"
                          size="sm"
                          onClick={() => setExpanded(expanded === widget.id ? null : widget.id)}
                        />
                        <Menu>
                          <MenuButton
                            as={IconButton}
                            icon={<FiMoreVertical />}
                            variant="ghost"
                            size="sm"
                          />
                          <MenuList>
                            <MenuItem icon={<FiRefreshCw />}>Refresh</MenuItem>
                            <MenuItem icon={<FiLayout />}>Settings</MenuItem>
                            <MenuItem
                              icon={expanded === widget.id ? <FiMinimize2 /> : <FiMaximize2 />}
                              onClick={() => setExpanded(expanded === widget.id ? null : widget.id)}
                            >
                              {expanded === widget.id ? 'Minimize' : 'Maximize'}
                            </MenuItem>
                            <MenuItem icon={<FiGrid />}>Change Size</MenuItem>
                            <MenuItem icon={<FiMove />}>Move to Top</MenuItem>
                          </MenuList>
                        </Menu>
                      </HStack>
                      {widget.component}
                    </Box>
                  </GridItem>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </Grid>
        )}
      </Droppable>
    </DragDropContext>
    </Box>
  );
};

export default DynamicLayout;



