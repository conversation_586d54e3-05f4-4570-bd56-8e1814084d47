# 🚀 Quick Start Guide - NexusFlow Backend

## Prerequisites Checklist

- [ ] Node.js v18+ installed
- [ ] MongoDB installed locally OR MongoDB Atlas account
- [ ] Git installed
- [ ] Code editor (VS Code recommended)

## 🏃‍♂️ Quick Setup (5 minutes)

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# Minimum required:
# - MONGODB_URI
# - JWT_SECRET
# - JWT_REFRESH_SECRET
```

### 3. Database Setup
```bash
# Run automated setup (includes database seeding)
npm run setup
```

### 4. Start Development Server
```bash
npm run dev
```

✅ **Backend is now running at http://localhost:5000**

## 🧪 Test the Setup

### 1. Health Check
```bash
curl http://localhost:5000/api/health
```

### 2. Login with Default Admin
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'
```

### 3. Get Products
```bash
curl http://localhost:5000/api/products
```

## 🔧 Configuration Options

### MongoDB Options

**Local MongoDB:**
```env
MONGODB_URI=mongodb://localhost:27017/nexusflow_ecommerce
```

**MongoDB Atlas:**
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/nexusflow_ecommerce
```

### Payment Gateway Setup

#### Stripe (Credit Cards)
1. Sign up at [stripe.com](https://stripe.com)
2. Get test API keys from dashboard
3. Add to .env:
```env
STRIPE_SECRET_KEY=sk_test_your_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
```

#### PayPal
1. Create developer account at [developer.paypal.com](https://developer.paypal.com)
2. Create sandbox application
3. Add to .env:
```env
PAYPAL_MODE=sandbox
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_client_secret
```

#### M-Pesa (Kenya Mobile Money)
1. Register at [developer.safaricom.co.ke](https://developer.safaricom.co.ke)
2. Create Lipa Na M-Pesa Online app
3. Add to .env:
```env
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_SHORTCODE=174379
MPESA_PASSKEY=your_passkey
```

## 📊 Default Data

After running `npm run setup`, you'll have:

### Users
- **Admin**: <EMAIL> / Admin123!
- **User 1**: <EMAIL> / User123!
- **User 2**: <EMAIL> / User123!

### Categories
- Electronics
- Accessories
- Home & Garden
- Sports & Fitness

### Products
- Premium Wireless Headphones
- Smart Fitness Watch
- Professional Camera Lens

## 🔍 Troubleshooting

### MongoDB Connection Issues
```bash
# Check if MongoDB is running
mongod --version

# Start MongoDB service (Windows)
net start MongoDB

# Start MongoDB service (macOS/Linux)
sudo systemctl start mongod
```

### Port Already in Use
```bash
# Find process using port 5000
netstat -ano | findstr :5000

# Kill process (Windows)
taskkill /PID <process_id> /F

# Kill process (macOS/Linux)
kill -9 <process_id>
```

### Environment Variables Not Loading
- Ensure .env file is in the backend directory
- Check for typos in variable names
- Restart the server after changing .env

### Payment Gateway Errors
- Verify API keys are correct
- Check if using sandbox/test mode
- Ensure webhook URLs are accessible

## 📈 Monitoring

### Logs Location
```
backend/logs/
├── combined.log    # All logs
├── error.log       # Error logs only
└── .gitkeep
```

### Health Check Endpoint
```http
GET /api/health
```

Response:
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600
}
```

## 🚀 Production Deployment

### Environment Variables
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://prod-user:<EMAIL>/nexusflow
JWT_SECRET=your-super-secure-production-secret
# ... other production configs
```

### PM2 Process Manager
```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start server.js --name "nexusflow-backend"

# Monitor
pm2 monit

# Logs
pm2 logs nexusflow-backend
```

### Docker Deployment
```bash
# Build image
docker build -t nexusflow-backend .

# Run container
docker run -p 5000:5000 --env-file .env nexusflow-backend
```

## 🔗 Integration with Frontend

Update your frontend environment variables:
```env
# Frontend .env
VITE_API_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000
```

## 📞 Support

- 📧 Email: <EMAIL>
- 📖 Full Documentation: [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

## 🎯 Next Steps

1. **Test Payment Integration**: Try making test payments with each gateway
2. **Customize Email Templates**: Update email templates in `utils/email.js`
3. **Add More Products**: Use the admin panel or API to add products
4. **Configure Webhooks**: Set up payment gateway webhooks for production
5. **Monitor Performance**: Check logs and system health regularly

---

**Happy coding! 🎉**
