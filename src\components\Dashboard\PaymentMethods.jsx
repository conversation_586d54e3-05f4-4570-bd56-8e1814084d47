import {
  Box,
  Text,
  HStack,
  Progress,
  Icon,
  Badge,
  SimpleGrid,
  useColorModeValue,
  Tooltip,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
} from '@chakra-ui/react';
import {
  FiCreditCard,
  FiSmartphone,
  FiDollarSign,
  FiShoppingBag,
} from 'react-icons/fi';
import {
  SiPaypal,
  SiBitcoin  // Changed from FiBitcoin to SiBitcoin
} from 'react-icons/si';

const PAYMENT_METHODS = [
  {
    id: 1,
    name: 'Credit Card',
    icon: FiCreditCard,
    usage: 45,
    trend: 2.5,
    amount: 156789,
    color: 'blue',
    providers: ['Visa', 'Mastercard', 'Amex'],
    details: {
      successRate: 98.5,
      avgTransactionValue: 127,
      peakHours: '14:00 - 18:00'
    }
  },
  {
    id: 2,
    name: 'PayPal',
    icon: SiPaypal,
    usage: 28,
    trend: 5.8,
    amount: 89456,
    color: 'cyan',
    providers: ['PayPal'],
    details: {
      successRate: 99.1,
      avgTransactionValue: 95,
      peakHours: '12:00 - 16:00'
    }
  },
  {
    id: 3,
    name: 'Digital Wallets',
    icon: FiSmartphone,
    usage: 15,
    trend: 12.3,
    amount: 45678,
    color: 'purple',
    providers: ['Apple Pay', 'Google Pay'],
    details: {
      successRate: 99.8,
      avgTransactionValue: 75,
      peakHours: '18:00 - 22:00'
    }
  },
  {
    id: 4,
    name: 'Cryptocurrency',
    icon: SiBitcoin,  // Changed from FiBitcoin to SiBitcoin
    usage: 7,
    trend: 15.7,
    amount: 23456,
    color: 'orange',
    providers: ['Bitcoin', 'Ethereum', 'USDT'],
    details: {
      successRate: 99.9,
      avgTransactionValue: 350,
      peakHours: '20:00 - 24:00'
    }
  },
  {
    id: 5,
    name: 'Bank Transfer',
    icon: FiDollarSign,
    usage: 3,
    trend: -1.2,
    amount: 12345,
    color: 'green',
    providers: ['ACH', 'Wire Transfer'],
    details: {
      successRate: 97.5,
      avgTransactionValue: 520,
      peakHours: '10:00 - 14:00'
    }
  },
  {
    id: 6,
    name: 'Buy Now Pay Later',
    icon: FiShoppingBag,
    usage: 2,
    trend: 25.4,
    amount: 8901,
    color: 'pink',
    providers: ['Affirm', 'Klarna', 'Afterpay'],
    details: {
      successRate: 95.0,
      avgTransactionValue: 225,
      peakHours: '16:00 - 20:00'
    }
  }
];

const PaymentMethodCard = ({ method }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
      transition="all 0.2s"
      _hover={{ transform: 'translateY(-2px)', shadow: 'md' }}
    >
      <HStack spacing={4} mb={4}>
        <Icon
          as={method.icon}
          boxSize={6}
          color={`${method.color}.500`}
        />
        <Box flex="1">
          <Text fontWeight="medium">{method.name}</Text>
          <HStack spacing={2}>
            {method.providers.map((provider, idx) => (
              <Badge
                key={idx}
                colorScheme={method.color}
                variant="subtle"
                fontSize="xs"
              >
                {provider}
              </Badge>
            ))}
          </HStack>
        </Box>
      </HStack>

      <Stat mb={4}>
        <StatLabel>Total Transactions</StatLabel>
        <StatNumber>${method.amount.toLocaleString()}</StatNumber>
        <StatHelpText>
          <StatArrow type={method.trend > 0 ? 'increase' : 'decrease'} />
          {Math.abs(method.trend)}%
        </StatHelpText>
      </Stat>

      <Tooltip
        label={`Success Rate: ${method.details.successRate}%
Avg Transaction: $${method.details.avgTransactionValue}
Peak Hours: ${method.details.peakHours}`}
        hasArrow
      >
        <Box>
          <Flex justify="space-between" mb={2}>
            <Text fontSize="sm">Usage Share</Text>
            <Text fontSize="sm" fontWeight="medium">{method.usage}%</Text>
          </Flex>
          <Progress
            value={method.usage}
            colorScheme={method.color}
            size="sm"
            borderRadius="full"
          />
        </Box>
      </Tooltip>
    </Box>
  );
};

const PaymentMethods = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');

  return (
    <Box p={4} bg={bgColor} borderRadius="xl">
      <HStack mb={6}>
        <Icon as={FiCreditCard} boxSize={6} color="blue.500" />
        <Text fontSize="xl" fontWeight="medium">Payment Methods Analysis</Text>
      </HStack>

      <SimpleGrid
        columns={{ base: 1, md: 2, lg: 3 }}
        spacing={6}
      >
        {PAYMENT_METHODS.map((method) => (
          <PaymentMethodCard key={method.id} method={method} />
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default PaymentMethods;

