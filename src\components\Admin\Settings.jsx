import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  Heading,
  FormControl,
  FormLabel,
  Input,
  Switch,
  Button,
  useToast,
  Divider,
  Card,
  CardHeader,
  CardBody,
  Select,
  HStack,
  Text,
  Badge,
  SimpleGrid,
  Textarea,
  IconButton,
  Tooltip
} from '@chakra-ui/react';
import {
  FiSave,
  FiRefreshCcw,
  FiDatabase,
  FiMail,
  FiShield,
  FiGlobe,
  FiInfo
} from 'react-icons/fi';

const Settings = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState({
    siteName: '',
    siteDescription: '',
    maintenanceMode: false,
    emailNotifications: true,
    backupFrequency: 'daily',
    analyticsEnabled: true,
    userRegistration: true,
    maxFileSize: '10',
    defaultUserRole: 'user',
    supportEmail: '',
    apiKey: '',
    debugMode: false
  });
  const toast = useToast();

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      // Simulated API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      setSettings({
        siteName: 'My Admin Dashboard',
        siteDescription: 'Powerful admin dashboard for managing users and content',
        maintenanceMode: false,
        emailNotifications: true,
        backupFrequency: 'daily',
        analyticsEnabled: true,
        userRegistration: true,
        maxFileSize: '10',
        defaultUserRole: 'user',
        supportEmail: '<EMAIL>',
        apiKey: 'sk_test_123456789',
        debugMode: false
      });
    } catch (error) {
      toast({
        title: 'Error fetching settings',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Settings saved',
        status: 'success',
        duration: 3000,
        isClosable: true
      });
    } catch (error) {
      toast({
        title: 'Error saving settings',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box p={4}>
      <VStack spacing={6} align="stretch">
        <HStack justify="space-between">
          <Heading size="lg">Admin Settings</Heading>
          <HStack>
            <Button
              leftIcon={<FiRefreshCcw />}
              variant="ghost"
              onClick={fetchSettings}
              isLoading={isLoading}
            >
              Refresh
            </Button>
            <Button
              leftIcon={<FiSave />}
              colorScheme="blue"
              onClick={handleSave}
              isLoading={isLoading}
            >
              Save Changes
            </Button>
          </HStack>
        </HStack>

        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          {/* General Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">General Settings</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>Site Name</FormLabel>
                  <Input
                    value={settings.siteName}
                    onChange={(e) => handleChange('siteName', e.target.value)}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Site Description</FormLabel>
                  <Textarea
                    value={settings.siteDescription}
                    onChange={(e) => handleChange('siteDescription', e.target.value)}
                  />
                </FormControl>
                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Maintenance Mode</FormLabel>
                  <Switch
                    isChecked={settings.maintenanceMode}
                    onChange={(e) => handleChange('maintenanceMode', e.target.checked)}
                  />
                </FormControl>
              </VStack>
            </CardBody>
          </Card>

          {/* Email Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">Email Settings</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>Support Email</FormLabel>
                  <Input
                    type="email"
                    value={settings.supportEmail}
                    onChange={(e) => handleChange('supportEmail', e.target.value)}
                  />
                </FormControl>
                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Email Notifications</FormLabel>
                  <Switch
                    isChecked={settings.emailNotifications}
                    onChange={(e) => handleChange('emailNotifications', e.target.checked)}
                  />
                </FormControl>
              </VStack>
            </CardBody>
          </Card>

          {/* Security Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">Security Settings</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>API Key</FormLabel>
                  <Input
                    type="password"
                    value={settings.apiKey}
                    onChange={(e) => handleChange('apiKey', e.target.value)}
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Default User Role</FormLabel>
                  <Select
                    value={settings.defaultUserRole}
                    onChange={(e) => handleChange('defaultUserRole', e.target.value)}
                  >
                    <option value="user">User</option>
                    <option value="editor">Editor</option>
                    <option value="admin">Admin</option>
                  </Select>
                </FormControl>
                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">User Registration</FormLabel>
                  <Switch
                    isChecked={settings.userRegistration}
                    onChange={(e) => handleChange('userRegistration', e.target.checked)}
                  />
                </FormControl>
              </VStack>
            </CardBody>
          </Card>

          {/* System Settings */}
          <Card>
            <CardHeader>
              <Heading size="md">System Settings</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>Backup Frequency</FormLabel>
                  <Select
                    value={settings.backupFrequency}
                    onChange={(e) => handleChange('backupFrequency', e.target.value)}
                  >
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </Select>
                </FormControl>
                <FormControl>
                  <FormLabel>Max File Size (MB)</FormLabel>
                  <Input
                    type="number"
                    value={settings.maxFileSize}
                    onChange={(e) => handleChange('maxFileSize', e.target.value)}
                  />
                </FormControl>
                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Debug Mode</FormLabel>
                  <Switch
                    isChecked={settings.debugMode}
                    onChange={(e) => handleChange('debugMode', e.target.checked)}
                  />
                </FormControl>
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>
      </VStack>
    </Box>
  );
};

export default Settings;