import { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Button,
  Icon,
  Input,
  InputGroup,
  InputRightElement,
  Checkbox,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Progress,
  Divider,
  Tooltip,
  Avatar
} from '@chakra-ui/react';
import { 
  FiCheckSquare, 
  FiPlus, 
  FiFilter, 
  FiClock, 
  FiFlag,
  FiMoreVertical,
  FiTrash2,
  FiEdit2,
  FiStar,
  FiCalendar,
  FiUser
} from 'react-icons/fi';

// Sample tasks data
const initialTasks = [
  {
    id: 1,
    title: 'Complete dashboard redesign',
    completed: false,
    priority: 'high',
    dueDate: '2023-05-15',
    assignee: { name: '<PERSON>', avatar: 'https://via.placeholder.com/150' },
    category: 'design'
  },
  {
    id: 2,
    title: 'Review new feature requirements',
    completed: false,
    priority: 'medium',
    dueDate: '2023-05-12',
    assignee: { name: '<PERSON>', avatar: 'https://via.placeholder.com/150' },
    category: 'planning'
  },
  {
    id: 3,
    title: 'Fix navigation bug in mobile view',
    completed: true,
    priority: 'high',
    dueDate: '2023-05-10',
    assignee: { name: 'John Doe', avatar: 'https://via.placeholder.com/150' },
    category: 'development'
  },
  {
    id: 4,
    title: 'Prepare presentation for client meeting',
    completed: false,
    priority: 'medium',
    dueDate: '2023-05-18',
    assignee: { name: 'Jane Smith', avatar: 'https://via.placeholder.com/150' },
    category: 'meeting'
  },
  {
    id: 5,
    title: 'Update API documentation',
    completed: false,
    priority: 'low',
    dueDate: '2023-05-20',
    assignee: { name: 'John Doe', avatar: 'https://via.placeholder.com/150' },
    category: 'documentation'
  },
  {
    id: 6,
    title: 'Deploy new version to production',
    completed: false,
    priority: 'high',
    dueDate: '2023-05-16',
    assignee: { name: 'Jane Smith', avatar: 'https://via.placeholder.com/150' },
    category: 'deployment'
  }
];

const TaskManager = () => {
  const [tasks, setTasks] = useState(initialTasks);
  const [filter, setFilter] = useState('all');
  const [newTaskTitle, setNewTaskTitle] = useState('');
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  
  const filteredTasks = tasks.filter(task => {
    if (filter === 'all') return true;
    if (filter === 'completed') return task.completed;
    if (filter === 'pending') return !task.completed;
    if (filter === 'high') return task.priority === 'high' && !task.completed;
    return true;
  });
  
  const completedCount = tasks.filter(task => task.completed).length;
  const completionPercentage = Math.round((completedCount / tasks.length) * 100);
  
  const handleToggleTask = (id) => {
    setTasks(tasks.map(task => 
      task.id === id ? { ...task, completed: !task.completed } : task
    ));
  };
  
  const handleAddTask = () => {
    if (!newTaskTitle.trim()) return;
    
    const newTask = {
      id: tasks.length + 1,
      title: newTaskTitle,
      completed: false,
      priority: 'medium',
      dueDate: new Date().toISOString().split('T')[0],
      assignee: { name: 'John Doe', avatar: 'https://via.placeholder.com/150' },
      category: 'general'
    };
    
    setTasks([...tasks, newTask]);
    setNewTaskTitle('');
  };
  
  const getPriorityColor = (priority) => {
    switch(priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'gray';
    }
  };
  
  const getCategoryIcon = (category) => {
    switch(category) {
      case 'design': return '🎨';
      case 'development': return '💻';
      case 'planning': return '📝';
      case 'meeting': return '👥';
      case 'documentation': return '📄';
      case 'deployment': return '🚀';
      default: return '📌';
    }
  };
  
  const formatDueDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  };
  
  const isDueSoon = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const diffTime = date - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 2 && diffDays >= 0;
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">
            <HStack>
              <Icon as={FiCheckSquare} />
              <Text>Task Manager</Text>
            </HStack>
          </Heading>
          <Text color={textColor} fontSize="sm">
            Track and manage your tasks
          </Text>
        </Box>
        <Menu>
          <MenuButton as={Button} size="sm" rightIcon={<FiFilter />} variant="outline">
            {filter === 'all' ? 'All Tasks' : 
             filter === 'completed' ? 'Completed' : 
             filter === 'pending' ? 'Pending' : 
             filter === 'high' ? 'High Priority' : 'Filter'}
          </MenuButton>
          <MenuList>
            <MenuItem onClick={() => setFilter('all')}>All Tasks</MenuItem>
            <MenuItem onClick={() => setFilter('pending')}>Pending</MenuItem>
            <MenuItem onClick={() => setFilter('completed')}>Completed</MenuItem>
            <MenuItem onClick={() => setFilter('high')}>High Priority</MenuItem>
          </MenuList>
        </Menu>
      </Flex>
      
      <HStack mb={4}>
        <Progress 
          value={completionPercentage} 
          size="sm" 
          colorScheme="green" 
          borderRadius="full"
          flex="1"
        />
        <Text fontSize="sm" fontWeight="medium" whiteSpace="nowrap">
          {completedCount} of {tasks.length} completed
        </Text>
      </HStack>
      
      <InputGroup size="md" mb={4}>
        <Input
          pr="4.5rem"
          placeholder="Add a new task..."
          value={newTaskTitle}
          onChange={(e) => setNewTaskTitle(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleAddTask()}
        />
        <InputRightElement width="4.5rem">
          <Button h="1.75rem" size="sm" onClick={handleAddTask}>
            <Icon as={FiPlus} />
          </Button>
        </InputRightElement>
      </InputGroup>
      
      <Box 
        overflowY="auto" 
        maxHeight={{ base: '300px', md: '400px' }}
        css={{
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            width: '6px',
            background: useColorModeValue('gray.100', 'gray.900'),
          },
          '&::-webkit-scrollbar-thumb': {
            background: useColorModeValue('gray.400', 'gray.700'),
            borderRadius: '24px',
          },
        }}
      >
        <VStack spacing={2} align="stretch">
          {filteredTasks.length === 0 ? (
            <Box textAlign="center" py={8}>
              <Icon as={FiCheckSquare} boxSize={10} color={textColor} mb={4} />
              <Text>No tasks found</Text>
            </Box>
          ) : (
            filteredTasks.map(task => (
              <Box
                key={task.id}
                p={3}
                borderRadius="md"
                borderWidth="1px"
                borderColor={borderColor}
                _hover={{ bg: hoverBg }}
                opacity={task.completed ? 0.7 : 1}
              >
                <Flex justify="space-between" align="flex-start">
                  <HStack align="flex-start" spacing={3} flex="1">
                    <Checkbox 
                      isChecked={task.completed}
                      onChange={() => handleToggleTask(task.id)}
                      colorScheme="green"
                      size="lg"
                    />
                    <Box>
                      <Text 
                        fontWeight="medium"
                        textDecoration={task.completed ? 'line-through' : 'none'}
                        color={task.completed ? textColor : 'inherit'}
                      >
                        {task.title}
                      </Text>
                      <HStack mt={1} fontSize="xs" color={textColor} spacing={3}>
                        <HStack>
                          <Icon as={FiCalendar} />
                          <Text>
                            {formatDueDate(task.dueDate)}
                            {isDueSoon(task.dueDate) && !task.completed && (
                              <Badge ml={1} colorScheme="red" variant="outline" fontSize="xx-small">
                                Soon
                              </Badge>
                            )}
                          </Text>
                        </HStack>
                        <HStack>
                          <Icon as={FiUser} />
                          <Text>{task.assignee.name}</Text>
                        </HStack>
                        <Text>{getCategoryIcon(task.category)}</Text>
                      </HStack>
                    </Box>
                  </HStack>
                  <HStack>
                    <Badge colorScheme={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                    <Menu>
                      <MenuButton
                        as={IconButton}
                        icon={<FiMoreVertical />}
                        variant="ghost"
                        size="sm"
                        aria-label="More options"
                      />
                      <MenuList>
                        <MenuItem icon={<FiEdit2 />}>Edit</MenuItem>
                        <MenuItem icon={<FiStar />}>Mark as Important</MenuItem>
                        <MenuItem icon={<FiClock />}>Reschedule</MenuItem>
                        <MenuItem icon={<FiTrash2 />} color="red.500">Delete</MenuItem>
                      </MenuList>
                    </Menu>
                  </HStack>
                </Flex>
              </Box>
            ))
          )}
        </VStack>
      </Box>
      
      <Divider my={4} />
      
      <Flex justify="space-between" align="center">
        <Text fontSize="xs" color={textColor}>
          <Icon as={FiClock} mr={1} />
          Updated {new Date().toLocaleTimeString()}
        </Text>
        <Button size="sm" variant="link" colorScheme="blue">
          View All Tasks
        </Button>
      </Flex>
    </Box>
  );
};

export default TaskManager;
