import {
  Container,
  Box,
  Heading,
  Text,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Button,
  VStack,
  SimpleGrid,
  Card,
  CardBody,
  Icon,
} from '@chakra-ui/react'
import { FiBook, FiMessageCircle, FiPhone, FiMail } from 'react-icons/fi'

function Help() {
  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box textAlign="center">
          <Heading mb={4}>How can we help you?</Heading>
          <Text color="gray.500">Find answers to common questions or contact our support team</Text>
        </Box>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          <SupportCard
            icon={FiBook}
            title="Documentation"
            description="Browse our detailed guides and documentation"
          />
          <SupportCard
            icon={FiMessageCircle}
            title="Live Chat"
            description="Chat with our support team"
          />
          <SupportCard
            icon={FiPhone}
            title="Phone Support"
            description="Call us at +****************"
          />
          <SupportCard
            icon={FiMail}
            title="Email"
            description="<EMAIL>"
          />
        </SimpleGrid>

        <Box>
          <Heading size="lg" mb={4}>Frequently Asked Questions</Heading>
          <Accordion allowMultiple>
            <FAQItem
              question="How do I get started?"
              answer="Sign up for an account, complete your profile, and start exploring our features."
            />
            <FAQItem
              question="What payment methods do you accept?"
              answer="We accept all major credit cards, PayPal, and bank transfers."
            />
            <FAQItem
              question="Can I upgrade my plan later?"
              answer="Yes, you can upgrade or downgrade your plan at any time."
            />
            <FAQItem
              question="How secure is my data?"
              answer="We use industry-standard encryption and security measures to protect your data."
            />
          </Accordion>
        </Box>

        <Box textAlign="center" bg="blue.50" p={8} borderRadius="lg">
          <Heading size="md" mb={4}>Still need help?</Heading>
          <Text mb={4}>Our support team is available 24/7 to assist you</Text>
          <Button colorScheme="blue" size="lg">Contact Support</Button>
        </Box>
      </VStack>
    </Container>
  )
}

const SupportCard = ({ icon, title, description }) => (
  <Card>
    <CardBody>
      <VStack spacing={4} align="center">
        <Icon as={icon} boxSize={8} color="blue.500" />
        <Heading size="md">{title}</Heading>
        <Text color="gray.500" textAlign="center">{description}</Text>
      </VStack>
    </CardBody>
  </Card>
)

const FAQItem = ({ question, answer }) => (
  <AccordionItem>
    <h2>
      <AccordionButton py={4}>
        <Box flex="1" textAlign="left" fontWeight="medium">
          {question}
        </Box>
        <AccordionIcon />
      </AccordionButton>
    </h2>
    <AccordionPanel pb={4}>
      {answer}
    </AccordionPanel>
  </AccordionItem>
)

export default Help