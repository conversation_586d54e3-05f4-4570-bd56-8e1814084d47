import { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Select,
  HStack,
  useColorModeValue,
  Button,
  ButtonGroup,
  Icon,
  Tooltip,
  Divider
} from '@chakra-ui/react';
import { FiDownload, FiRefreshCw, FiInfo } from 'react-icons/fi';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts';

// Sample data
const generateData = (period) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const weeks = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  
  let labels = [];
  let dataPoints = [];
  let comparisonPoints = [];
  
  switch(period) {
    case 'yearly':
      labels = months;
      dataPoints = [18500, 21200, 19800, 22400, 24600, 23100, 26800, 27200, 25900, 29300, 31200, 32800];
      comparisonPoints = [15200, 16800, 17500, 18900, 19200, 20100, 22300, 23100, 22800, 24500, 26100, 27400];
      break;
    case 'monthly':
      labels = weeks;
      dataPoints = [5800, 6200, 7100, 6900];
      comparisonPoints = [4900, 5300, 5800, 5600];
      break;
    case 'weekly':
      labels = days;
      dataPoints = [820, 932, 901, 934, 1290, 1330, 1320];
      comparisonPoints = [720, 832, 801, 834, 1090, 1130, 1120];
      break;
    default:
      labels = months;
      dataPoints = [18500, 21200, 19800, 22400, 24600, 23100, 26800, 27200, 25900, 29300, 31200, 32800];
      comparisonPoints = [15200, 16800, 17500, 18900, 19200, 20100, 22300, 23100, 22800, 24500, 26100, 27400];
  }
  
  return labels.map((label, index) => ({
    name: label,
    current: dataPoints[index],
    previous: comparisonPoints[index],
  }));
};

const SalesChart = () => {
  const [period, setPeriod] = useState('yearly');
  const [chartType, setChartType] = useState('line');
  const data = generateData(period);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const activeButtonBg = useColorModeValue('gray.100', 'gray.700');
  
  const currentColor = useColorModeValue('#3182CE', '#63B3ED'); // blue.500, blue.300
  const previousColor = useColorModeValue('#CBD5E0', '#4A5568'); // gray.300, gray.600
  
  const renderChart = () => {
    switch(chartType) {
      case 'bar':
        return (
          <BarChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke={borderColor} />
            <XAxis dataKey="name" tick={{ fill: textColor }} />
            <YAxis tick={{ fill: textColor }} />
            <RechartsTooltip 
              contentStyle={{ 
                backgroundColor: bgColor, 
                borderColor: borderColor,
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Bar dataKey="current" name="Current Period" fill={currentColor} radius={[4, 4, 0, 0]} />
            <Bar dataKey="previous" name="Previous Period" fill={previousColor} radius={[4, 4, 0, 0]} />
          </BarChart>
        );
      case 'area':
        return (
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke={borderColor} />
            <XAxis dataKey="name" tick={{ fill: textColor }} />
            <YAxis tick={{ fill: textColor }} />
            <RechartsTooltip 
              contentStyle={{ 
                backgroundColor: bgColor, 
                borderColor: borderColor,
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Area type="monotone" dataKey="current" name="Current Period" stroke={currentColor} fill={currentColor} fillOpacity={0.3} />
            <Area type="monotone" dataKey="previous" name="Previous Period" stroke={previousColor} fill={previousColor} fillOpacity={0.3} />
          </AreaChart>
        );
      case 'line':
      default:
        return (
          <LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke={borderColor} />
            <XAxis dataKey="name" tick={{ fill: textColor }} />
            <YAxis tick={{ fill: textColor }} />
            <RechartsTooltip 
              contentStyle={{ 
                backgroundColor: bgColor, 
                borderColor: borderColor,
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }} 
            />
            <Legend />
            <Line type="monotone" dataKey="current" name="Current Period" stroke={currentColor} strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
            <Line type="monotone" dataKey="previous" name="Previous Period" stroke={previousColor} strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
          </LineChart>
        );
    }
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">Sales Overview</Heading>
          <Text color={textColor} fontSize="sm">
            Comparing current vs previous period
          </Text>
        </Box>
        <HStack spacing={2}>
          <Select 
            size="sm" 
            value={period} 
            onChange={(e) => setPeriod(e.target.value)}
            width={{ base: '110px', md: '130px' }}
          >
            <option value="yearly">Yearly</option>
            <option value="monthly">Monthly</option>
            <option value="weekly">Weekly</option>
          </Select>
          <Tooltip label="Download data">
            <Button size="sm" variant="ghost">
              <Icon as={FiDownload} />
            </Button>
          </Tooltip>
          <Tooltip label="Refresh data">
            <Button size="sm" variant="ghost">
              <Icon as={FiRefreshCw} />
            </Button>
          </Tooltip>
        </HStack>
      </Flex>
      
      <ButtonGroup size="sm" isAttached variant="outline" mb={4}>
        <Button 
          onClick={() => setChartType('line')}
          bg={chartType === 'line' ? activeButtonBg : 'transparent'}
        >
          Line
        </Button>
        <Button 
          onClick={() => setChartType('bar')}
          bg={chartType === 'bar' ? activeButtonBg : 'transparent'}
        >
          Bar
        </Button>
        <Button 
          onClick={() => setChartType('area')}
          bg={chartType === 'area' ? activeButtonBg : 'transparent'}
        >
          Area
        </Button>
      </ButtonGroup>
      
      <Box height={{ base: '250px', md: '300px', lg: '350px' }}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </Box>
      
      <Divider my={4} />
      
      <Flex justify="space-between" align="center">
        <HStack>
          <Icon as={FiInfo} color={textColor} />
          <Text fontSize="xs" color={textColor}>
            Last updated: {new Date().toLocaleDateString()}
          </Text>
        </HStack>
        <Text fontSize="xs" color={textColor}>
          {period === 'yearly' ? 'Year to date' : period === 'monthly' ? 'Month to date' : 'Week to date'} growth: 
          <Text as="span" color="green.500" fontWeight="bold" ml={1}>
            +18.4%
          </Text>
        </Text>
      </Flex>
    </Box>
  );
};

export default SalesChart;
