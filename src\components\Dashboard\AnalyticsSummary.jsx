import { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Heading,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  SimpleGrid,
  Icon,
  useColorModeValue,
  Divider,
  HStack,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Select
} from '@chakra-ui/react';
import {
  FiTrendingUp,
  FiUsers,
  FiDollarSign,
  FiShoppingCart,
  FiCalendar,
  FiMoreVertical
} from 'react-icons/fi';

const StatCard = ({ title, value, change, icon, period }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const isPositive = change >= 0;
  
  return (
    <Box
      bg={bgColor}
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      transition="transform 0.3s"
      _hover={{ transform: 'translateY(-4px)', boxShadow: 'md' }}
    >
      <Flex justify="space-between" align="flex-start">
        <Box>
          <StatLabel color="gray.500" fontSize="sm">{title}</StatLabel>
          <StatNumber fontSize="2xl" fontWeight="bold" mt={1}>{value}</StatNumber>
          <StatHelpText mb={0}>
            <StatArrow type={isPositive ? 'increase' : 'decrease'} />
            {Math.abs(change)}% vs {period}
          </StatHelpText>
        </Box>
        <Icon as={icon} boxSize={6} color={isPositive ? 'green.500' : 'red.500'} />
      </Flex>
    </Box>
  );
};

const AnalyticsSummary = () => {
  const [period, setPeriod] = useState('Last Month');
  const [stats, setStats] = useState([
    { title: 'Total Revenue', value: '$24,532', change: 12.5, icon: FiDollarSign },
    { title: 'New Customers', value: '321', change: 8.2, icon: FiUsers },
    { title: 'Total Orders', value: '1,463', change: -3.1, icon: FiShoppingCart },
    { title: 'Conversion Rate', value: '3.2%', change: 1.8, icon: FiTrendingUp }
  ]);

  // Simulate data change when period changes
  useEffect(() => {
    // In a real app, this would fetch data based on the selected period
    const variations = {
      'Last Week': [
        { title: 'Total Revenue', value: '$5,832', change: 8.3, icon: FiDollarSign },
        { title: 'New Customers', value: '87', change: 12.1, icon: FiUsers },
        { title: 'Total Orders', value: '342', change: 5.7, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '3.5%', change: 2.2, icon: FiTrendingUp }
      ],
      'Last Month': [
        { title: 'Total Revenue', value: '$24,532', change: 12.5, icon: FiDollarSign },
        { title: 'New Customers', value: '321', change: 8.2, icon: FiUsers },
        { title: 'Total Orders', value: '1,463', change: -3.1, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '3.2%', change: 1.8, icon: FiTrendingUp }
      ],
      'Last Quarter': [
        { title: 'Total Revenue', value: '$78,215', change: 15.8, icon: FiDollarSign },
        { title: 'New Customers', value: '943', change: -2.4, icon: FiUsers },
        { title: 'Total Orders', value: '4,271', change: 7.3, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '2.9%', change: -0.5, icon: FiTrendingUp }
      ],
      'Last Year': [
        { title: 'Total Revenue', value: '$312,754', change: 22.7, icon: FiDollarSign },
        { title: 'New Customers', value: '3,842', change: 18.9, icon: FiUsers },
        { title: 'Total Orders', value: '17,583', change: 14.2, icon: FiShoppingCart },
        { title: 'Conversion Rate', value: '3.1%', change: 0.7, icon: FiTrendingUp }
      ]
    };

    setStats(variations[period] || variations['Last Month']);
  }, [period]);

  return (
    <Box>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">Analytics Summary</Heading>
        <HStack>
          <Select 
            size="sm" 
            value={period} 
            onChange={(e) => setPeriod(e.target.value)}
            width="150px"
          >
            <option value="Last Week">Last Week</option>
            <option value="Last Month">Last Month</option>
            <option value="Last Quarter">Last Quarter</option>
            <option value="Last Year">Last Year</option>
          </Select>
          <Menu>
            <MenuButton as={Button} size="sm" variant="ghost">
              <Icon as={FiMoreVertical} />
            </MenuButton>
            <MenuList>
              <MenuItem>Download Report</MenuItem>
              <MenuItem>Share Analytics</MenuItem>
              <MenuItem>View Detailed Report</MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>

      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
        {stats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            icon={stat.icon}
            period={period}
          />
        ))}
      </SimpleGrid>

      <Divider my={6} />

      <Flex justify="space-between" align="center">
        <Text color="gray.500" fontSize="sm">
          <Icon as={FiCalendar} mr={1} />
          Last updated: {new Date().toLocaleDateString()}
        </Text>
        <Button size="sm" leftIcon={<Icon as={FiTrendingUp} />} colorScheme="blue" variant="outline">
          View Full Analytics
        </Button>
      </Flex>
    </Box>
  );
};

export default AnalyticsSummary;
