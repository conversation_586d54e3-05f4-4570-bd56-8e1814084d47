import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  Avatar,
  Badge,
  Button,
  useColorModeValue,
  Divider,
  Icon,
  AvatarGroup,
} from '@chakra-ui/react';
import { FiClock, FiArrowRight, FiDollarSign, FiPackage } from 'react-icons/fi';
import { Link as RouterLink } from 'react-router-dom';

const RecentCustomers = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const recentCustomers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      purchase: 'Premium Headphones',
      amount: 199.99,
      time: '10 minutes ago',
      status: 'completed',
      avatar: 'https://bit.ly/sage-adebayo',
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      purchase: 'Wireless Earbuds',
      amount: 149.99,
      time: '25 minutes ago',
      status: 'processing',
      avatar: 'https://bit.ly/ryan-florence',
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      purchase: 'Smart Watch',
      amount: 299.99,
      time: '1 hour ago',
      status: 'completed',
      avatar: 'https://bit.ly/prosper-baba',
    },
  ];

  const getStatusColor = (status) => {
    const colors = {
      completed: 'green',
      processing: 'orange',
      pending: 'yellow',
      failed: 'red',
    };
    return colors[status] || 'gray';
  };

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack justify="space-between" mb={6}>
        <Text fontSize="lg" fontWeight="medium">Recent Customers</Text>
        <AvatarGroup size="sm" max={3}>
          {recentCustomers.map((customer) => (
            <Avatar
              key={customer.id}
              name={customer.name}
              src={customer.avatar}
            />
          ))}
        </AvatarGroup>
      </HStack>

      <VStack spacing={4} align="stretch">
        {recentCustomers.map((customer, index) => (
          <React.Fragment key={customer.id}>
            <HStack justify="space-between">
              <HStack spacing={3}>
                <Avatar size="sm" name={customer.name} src={customer.avatar} />
                <Box>
                  <Text fontWeight="medium">{customer.name}</Text>
                  <Text fontSize="sm" color="gray.500">{customer.email}</Text>
                </Box>
              </HStack>
              <Box textAlign="right">
                <HStack spacing={2} justify="flex-end">
                  <Icon as={FiDollarSign} color="green.500" />
                  <Text fontWeight="medium">${customer.amount}</Text>
                </HStack>
                <HStack spacing={2} justify="flex-end">
                  <Icon as={FiClock} color="gray.500" size="sm" />
                  <Text fontSize="sm" color="gray.500">{customer.time}</Text>
                </HStack>
              </Box>
            </HStack>
            <HStack px={12} spacing={2}>
              <Icon as={FiPackage} color="gray.500" />
              <Text fontSize="sm">{customer.purchase}</Text>
              <Badge colorScheme={getStatusColor(customer.status)}>
                {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
              </Badge>
            </HStack>
            {index < recentCustomers.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </VStack>

      <Button
        as={RouterLink}
        to="/customer-details"
        rightIcon={<FiArrowRight />}
        variant="ghost"
        colorScheme="blue"
        size="sm"
        mt={6}
        width="full"
      >
        View All Customers
      </Button>
    </Box>
  );
};

export default RecentCustomers;