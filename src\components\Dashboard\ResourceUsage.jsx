import { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Button,
  Icon,
  Progress,
  CircularProgress,
  CircularProgressLabel,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  SimpleGrid,
  Divider,
  Tooltip
} from '@chakra-ui/react';
import { 
  FiHardDrive, 
  FiCpu, 
  FiDatabase, 
  FiWifi, 
  FiAlertCircle,
  FiRefreshCw,
  FiDownload,
  FiUpload
} from 'react-icons/fi';

const ResourceUsage = () => {
  const [resources, setResources] = useState({
    storage: { used: 1.8, total: 5, unit: 'TB', percentage: 36 },
    bandwidth: { used: 450, total: 1000, unit: 'GB', percentage: 45 },
    cpu: { percentage: 28 },
    memory: { percentage: 62 },
    database: { percentage: 75 }
  });
  
  const [networkStats, setNetworkStats] = useState({
    download: { current: 12.5, previous: 10.2, unit: 'MB/s' },
    upload: { current: 3.8, previous: 4.2, unit: 'MB/s' },
    latency: { current: 28, previous: 32, unit: 'ms' },
    uptime: { current: 99.98, previous: 99.95, unit: '%' }
  });
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  
  // Simulate resource usage changes
  useEffect(() => {
    const interval = setInterval(() => {
      setResources(prev => ({
        ...prev,
        cpu: { percentage: Math.min(100, Math.max(10, prev.cpu.percentage + (Math.random() * 10 - 5))) },
        memory: { percentage: Math.min(100, Math.max(20, prev.memory.percentage + (Math.random() * 8 - 4))) }
      }));
      
      setNetworkStats(prev => ({
        ...prev,
        download: { 
          ...prev.download, 
          current: parseFloat((prev.download.current + (Math.random() * 2 - 1)).toFixed(1))
        },
        upload: { 
          ...prev.upload, 
          current: parseFloat((prev.upload.current + (Math.random() * 1 - 0.5)).toFixed(1))
        }
      }));
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  const getProgressColor = (percentage) => {
    if (percentage < 50) return 'green';
    if (percentage < 75) return 'yellow';
    return 'red';
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">
            <HStack>
              <Icon as={FiHardDrive} />
              <Text>Resource Usage</Text>
            </HStack>
          </Heading>
          <Text color={textColor} fontSize="sm">
            System resources and network performance
          </Text>
        </Box>
        <Tooltip label="Refresh data">
          <Button size="sm" variant="ghost">
            <Icon as={FiRefreshCw} />
          </Button>
        </Tooltip>
      </Flex>
      
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
        {/* Storage and Bandwidth */}
        <Box>
          <Heading size="sm" mb={3}>Storage & Bandwidth</Heading>
          
          <VStack spacing={4} align="stretch">
            <Box>
              <Flex justify="space-between" mb={1}>
                <HStack>
                  <Icon as={FiHardDrive} color="blue.500" />
                  <Text fontWeight="medium">Storage</Text>
                </HStack>
                <Text fontSize="sm">
                  {resources.storage.used} / {resources.storage.total} {resources.storage.unit}
                </Text>
              </Flex>
              <Progress 
                value={resources.storage.percentage} 
                colorScheme={getProgressColor(resources.storage.percentage)}
                borderRadius="full"
                size="sm"
              />
            </Box>
            
            <Box>
              <Flex justify="space-between" mb={1}>
                <HStack>
                  <Icon as={FiWifi} color="purple.500" />
                  <Text fontWeight="medium">Bandwidth</Text>
                </HStack>
                <Text fontSize="sm">
                  {resources.bandwidth.used} / {resources.bandwidth.total} {resources.bandwidth.unit}
                </Text>
              </Flex>
              <Progress 
                value={resources.bandwidth.percentage} 
                colorScheme={getProgressColor(resources.bandwidth.percentage)}
                borderRadius="full"
                size="sm"
              />
            </Box>
          </VStack>
          
          <SimpleGrid columns={2} spacing={4} mt={6}>
            <Stat>
              <StatLabel>
                <HStack>
                  <Icon as={FiDownload} color="green.500" />
                  <Text>Download</Text>
                </HStack>
              </StatLabel>
              <StatNumber>{networkStats.download.current} {networkStats.download.unit}</StatNumber>
              <StatHelpText>
                <StatArrow 
                  type={networkStats.download.current >= networkStats.download.previous ? 'increase' : 'decrease'} 
                />
                {Math.abs(((networkStats.download.current - networkStats.download.previous) / networkStats.download.previous * 100).toFixed(1))}%
              </StatHelpText>
            </Stat>
            
            <Stat>
              <StatLabel>
                <HStack>
                  <Icon as={FiUpload} color="blue.500" />
                  <Text>Upload</Text>
                </HStack>
              </StatLabel>
              <StatNumber>{networkStats.upload.current} {networkStats.upload.unit}</StatNumber>
              <StatHelpText>
                <StatArrow 
                  type={networkStats.upload.current >= networkStats.upload.previous ? 'increase' : 'decrease'} 
                />
                {Math.abs(((networkStats.upload.current - networkStats.upload.previous) / networkStats.upload.previous * 100).toFixed(1))}%
              </StatHelpText>
            </Stat>
          </SimpleGrid>
        </Box>
        
        {/* CPU, Memory and Database */}
        <Box>
          <Heading size="sm" mb={3}>System Resources</Heading>
          
          <Flex justify="space-around" mb={6}>
            <VStack>
              <CircularProgress 
                value={resources.cpu.percentage} 
                color={`${getProgressColor(resources.cpu.percentage)}.500`}
                size="100px"
                thickness="8px"
              >
                <CircularProgressLabel>{resources.cpu.percentage}%</CircularProgressLabel>
              </CircularProgress>
              <HStack>
                <Icon as={FiCpu} />
                <Text fontWeight="medium">CPU</Text>
              </HStack>
            </VStack>
            
            <VStack>
              <CircularProgress 
                value={resources.memory.percentage} 
                color={`${getProgressColor(resources.memory.percentage)}.500`}
                size="100px"
                thickness="8px"
              >
                <CircularProgressLabel>{resources.memory.percentage}%</CircularProgressLabel>
              </CircularProgress>
              <HStack>
                <Icon as={FiDatabase} />
                <Text fontWeight="medium">Memory</Text>
              </HStack>
            </VStack>
          </Flex>
          
          <Box>
            <Flex justify="space-between" mb={1}>
              <HStack>
                <Icon as={FiDatabase} color="orange.500" />
                <Text fontWeight="medium">Database Load</Text>
              </HStack>
              <Text fontSize="sm">
                {resources.database.percentage}%
              </Text>
            </Flex>
            <Progress 
              value={resources.database.percentage} 
              colorScheme={getProgressColor(resources.database.percentage)}
              borderRadius="full"
              size="sm"
            />
          </Box>
          
          {resources.database.percentage > 70 && (
            <HStack mt={2} color="orange.500" fontSize="sm">
              <Icon as={FiAlertCircle} />
              <Text>High database load detected</Text>
            </HStack>
          )}
        </Box>
      </SimpleGrid>
      
      <Divider my={4} />
      
      <Flex justify="space-between" align="center">
        <Text fontSize="xs" color={textColor}>
          Last updated: {new Date().toLocaleTimeString()}
        </Text>
        <Button size="sm" variant="link" colorScheme="blue">
          View Detailed Metrics
        </Button>
      </Flex>
    </Box>
  );
};

export default ResourceUsage;
