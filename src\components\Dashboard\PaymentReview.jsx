import React from 'react';
import {
  Box,
  Text,
  HStack,
  VStack,
  Icon,
  Button,
  useColorModeValue,
  Badge,
} from '@chakra-ui/react';
import { FiDollarSign, FiArrowRight, FiTrendingUp } from 'react-icons/fi';
import { Link as RouterLink } from 'react-router-dom';

const PaymentReview = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const recentPayments = [
    {
      id: 1,
      amount: '$1,234.56',
      status: 'completed',
      method: 'Credit Card',
      date: '2024-01-15',
    },
    {
      id: 2,
      amount: '$890.00',
      status: 'pending',
      method: 'PayPal',
      date: '2024-01-14',
    },
    {
      id: 3,
      amount: '$2,500.00',
      status: 'completed',
      method: 'Bank Transfer',
      date: '2024-01-13',
    },
  ];

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack justify="space-between" mb={4}>
        <HStack>
          <Icon as={FiDollarSign} boxSize={6} color="green.500" />
          <Text fontSize="lg" fontWeight="medium">Payment Review</Text>
        </HStack>
        <Badge colorScheme="green" display="flex" alignItems="center" px={2} py={1}>
          <Icon as={FiTrendingUp} mr={1} />
          15.2% ↑
        </Badge>
      </HStack>

      <VStack spacing={4} align="stretch">
        {recentPayments.map((payment) => (
          <Box
            key={payment.id}
            p={3}
            borderRadius="md"
            bg={useColorModeValue('gray.50', 'gray.700')}
          >
            <HStack justify="space-between">
              <VStack align="start" spacing={1}>
                <Text fontWeight="medium">{payment.amount}</Text>
                <Text fontSize="sm" color="gray.500">
                  {payment.method}
                </Text>
              </VStack>
              <VStack align="end" spacing={1}>
                <Badge
                  colorScheme={payment.status === 'completed' ? 'green' : 'yellow'}
                >
                  {payment.status}
                </Badge>
                <Text fontSize="sm" color="gray.500">
                  {payment.date}
                </Text>
              </VStack>
            </HStack>
          </Box>
        ))}
      </VStack>

      <Button
        as={RouterLink}
        to="/payments/review"
        rightIcon={<FiArrowRight />}
        variant="ghost"
        colorScheme="green"
        size="sm"
        mt={4}
        width="full"
      >
        View All Payments
      </Button>
    </Box>
  );
};

export default PaymentReview;