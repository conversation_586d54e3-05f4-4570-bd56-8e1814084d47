import express from 'express';
import { body, validationResult } from 'express-validator';
import Strip<PERSON> from 'stripe';
import paypal from 'paypal-rest-sdk';
import axios from 'axios';
import { protect } from '../middleware/auth.js';
import Order from '../models/Order.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Initialize payment gateways
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

paypal.configure({
  mode: process.env.PAYPAL_MODE || 'sandbox',
  client_id: process.env.PAYPAL_CLIENT_ID,
  client_secret: process.env.PAYPAL_CLIENT_SECRET
});

// M-Pesa configuration
const MPESA_CONFIG = {
  consumerKey: process.env.MPESA_CONSUMER_KEY,
  consumerSecret: process.env.MPESA_CONSUMER_SECRET,
  environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
  shortCode: process.env.MPESA_SHORTCODE,
  passkey: process.env.MPESA_PASSKEY,
  callbackUrl: process.env.MPESA_CALLBACK_URL
};

// Helper function to get M-Pesa access token
const getMpesaAccessToken = async () => {
  try {
    const auth = Buffer.from(`${MPESA_CONFIG.consumerKey}:${MPESA_CONFIG.consumerSecret}`).toString('base64');
    const url = MPESA_CONFIG.environment === 'production' 
      ? 'https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials'
      : 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials';

    const response = await axios.get(url, {
      headers: {
        Authorization: `Basic ${auth}`
      }
    });

    return response.data.access_token;
  } catch (error) {
    logger.error('M-Pesa access token error:', error);
    throw new Error('Failed to get M-Pesa access token');
  }
};

// @desc    Create Stripe payment intent
// @route   POST /api/payments/stripe/create-intent
// @access  Private
router.post('/stripe/create-intent', protect, [
  body('amount').isNumeric().withMessage('Amount must be a number'),
  body('currency').optional().isLength({ min: 3, max: 3 }).withMessage('Currency must be 3 characters'),
  body('orderId').isMongoId().withMessage('Valid order ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { amount, currency = 'usd', orderId } = req.body;

    // Verify order exists and belongs to user
    const order = await Order.findOne({ _id: orderId, user: req.user.id });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      metadata: {
        orderId: orderId,
        userId: req.user.id
      }
    });

    // Update order with payment intent ID
    order.payment.paymentIntentId = paymentIntent.id;
    order.payment.status = 'processing';
    await order.save();

    logger.info(`Stripe payment intent created: ${paymentIntent.id} for order: ${orderId}`);

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id
      }
    });

  } catch (error) {
    logger.error('Stripe payment intent error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment intent',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Confirm Stripe payment
// @route   POST /api/payments/stripe/confirm
// @access  Private
router.post('/stripe/confirm', protect, [
  body('paymentIntentId').notEmpty().withMessage('Payment intent ID is required')
], async (req, res) => {
  try {
    const { paymentIntentId } = req.body;

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status === 'succeeded') {
      // Update order payment status
      const order = await Order.findOne({ 
        'payment.paymentIntentId': paymentIntentId,
        user: req.user.id 
      });

      if (order) {
        order.payment.status = 'completed';
        order.payment.transactionId = paymentIntent.id;
        order.payment.paidAt = new Date();
        order.updateStatus('confirmed', 'Payment completed via Stripe');
        await order.save();

        logger.info(`Stripe payment confirmed for order: ${order._id}`);
      }

      res.json({
        success: true,
        message: 'Payment confirmed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Payment not completed'
      });
    }

  } catch (error) {
    logger.error('Stripe payment confirmation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to confirm payment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Create PayPal payment
// @route   POST /api/payments/paypal/create
// @access  Private
router.post('/paypal/create', protect, [
  body('amount').isNumeric().withMessage('Amount must be a number'),
  body('currency').optional().isLength({ min: 3, max: 3 }).withMessage('Currency must be 3 characters'),
  body('orderId').isMongoId().withMessage('Valid order ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { amount, currency = 'USD', orderId } = req.body;

    // Verify order exists and belongs to user
    const order = await Order.findOne({ _id: orderId, user: req.user.id });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    const paymentData = {
      intent: 'sale',
      payer: {
        payment_method: 'paypal'
      },
      redirect_urls: {
        return_url: `${process.env.CLIENT_URL}/payment/success`,
        cancel_url: `${process.env.CLIENT_URL}/payment/cancel`
      },
      transactions: [{
        amount: {
          total: amount.toString(),
          currency: currency
        },
        description: `Order #${order.orderNumber}`,
        custom: orderId
      }]
    };

    paypal.payment.create(paymentData, (error, payment) => {
      if (error) {
        logger.error('PayPal payment creation error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to create PayPal payment'
        });
      }

      const approvalUrl = payment.links.find(link => link.rel === 'approval_url');
      
      logger.info(`PayPal payment created: ${payment.id} for order: ${orderId}`);

      res.json({
        success: true,
        data: {
          paymentId: payment.id,
          approvalUrl: approvalUrl.href
        }
      });
    });

  } catch (error) {
    logger.error('PayPal payment creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create PayPal payment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Execute PayPal payment
// @route   POST /api/payments/paypal/execute
// @access  Private
router.post('/paypal/execute', protect, [
  body('paymentId').notEmpty().withMessage('Payment ID is required'),
  body('payerId').notEmpty().withMessage('Payer ID is required')
], async (req, res) => {
  try {
    const { paymentId, payerId } = req.body;

    const executeData = {
      payer_id: payerId
    };

    paypal.payment.execute(paymentId, executeData, async (error, payment) => {
      if (error) {
        logger.error('PayPal payment execution error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to execute PayPal payment'
        });
      }

      if (payment.state === 'approved') {
        // Update order payment status
        const orderId = payment.transactions[0].custom;
        const order = await Order.findOne({ _id: orderId, user: req.user.id });

        if (order) {
          order.payment.status = 'completed';
          order.payment.paypalTransactionId = payment.id;
          order.payment.transactionId = payment.transactions[0].related_resources[0].sale.id;
          order.payment.paidAt = new Date();
          order.updateStatus('confirmed', 'Payment completed via PayPal');
          await order.save();

          logger.info(`PayPal payment executed for order: ${order._id}`);
        }

        res.json({
          success: true,
          message: 'Payment executed successfully',
          data: {
            transactionId: payment.transactions[0].related_resources[0].sale.id
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Payment not approved'
        });
      }
    });

  } catch (error) {
    logger.error('PayPal payment execution error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to execute PayPal payment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Initiate M-Pesa STK Push
// @route   POST /api/payments/mpesa/stk-push
// @access  Private
router.post('/mpesa/stk-push', protect, [
  body('phoneNumber').matches(/^254\d{9}$/).withMessage('Phone number must be in format 254XXXXXXXXX'),
  body('amount').isNumeric().withMessage('Amount must be a number'),
  body('orderId').isMongoId().withMessage('Valid order ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { phoneNumber, amount, orderId } = req.body;

    // Verify order exists and belongs to user
    const order = await Order.findOne({ _id: orderId, user: req.user.id });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Get M-Pesa access token
    const accessToken = await getMpesaAccessToken();

    // Generate timestamp and password
    const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
    const password = Buffer.from(`${MPESA_CONFIG.shortCode}${MPESA_CONFIG.passkey}${timestamp}`).toString('base64');

    const stkPushData = {
      BusinessShortCode: MPESA_CONFIG.shortCode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: Math.round(amount),
      PartyA: phoneNumber,
      PartyB: MPESA_CONFIG.shortCode,
      PhoneNumber: phoneNumber,
      CallBackURL: `${MPESA_CONFIG.callbackUrl}/api/payments/mpesa/callback`,
      AccountReference: order.orderNumber,
      TransactionDesc: `Payment for Order ${order.orderNumber}`
    };

    const url = MPESA_CONFIG.environment === 'production'
      ? 'https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest'
      : 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest';

    const response = await axios.post(url, stkPushData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.ResponseCode === '0') {
      // Update order with M-Pesa checkout request ID
      order.payment.method = 'mpesa';
      order.payment.status = 'processing';
      order.payment.transactionId = response.data.CheckoutRequestID;
      await order.save();

      logger.info(`M-Pesa STK push initiated: ${response.data.CheckoutRequestID} for order: ${orderId}`);

      res.json({
        success: true,
        message: 'STK push sent successfully',
        data: {
          checkoutRequestId: response.data.CheckoutRequestID,
          merchantRequestId: response.data.MerchantRequestID
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: response.data.ResponseDescription || 'STK push failed'
      });
    }

  } catch (error) {
    logger.error('M-Pesa STK push error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate M-Pesa payment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    M-Pesa callback handler
// @route   POST /api/payments/mpesa/callback
// @access  Public (M-Pesa callback)
router.post('/mpesa/callback', async (req, res) => {
  try {
    const { Body } = req.body;
    const { stkCallback } = Body;

    logger.info('M-Pesa callback received:', JSON.stringify(stkCallback));

    if (stkCallback.ResultCode === 0) {
      // Payment successful
      const checkoutRequestId = stkCallback.CheckoutRequestID;
      const mpesaReceiptNumber = stkCallback.CallbackMetadata.Item.find(
        item => item.Name === 'MpesaReceiptNumber'
      )?.Value;

      // Find and update order
      const order = await Order.findOne({ 'payment.transactionId': checkoutRequestId });

      if (order) {
        order.payment.status = 'completed';
        order.payment.mpesaReceiptNumber = mpesaReceiptNumber;
        order.payment.paidAt = new Date();
        order.updateStatus('confirmed', 'Payment completed via M-Pesa');
        await order.save();

        logger.info(`M-Pesa payment completed for order: ${order._id}, Receipt: ${mpesaReceiptNumber}`);

        // Emit real-time notification to user
        const io = req.app.get('io');
        io.to(`user_${order.user}`).emit('payment_success', {
          orderId: order._id,
          receiptNumber: mpesaReceiptNumber
        });
      }
    } else {
      // Payment failed
      const checkoutRequestId = stkCallback.CheckoutRequestID;
      const order = await Order.findOne({ 'payment.transactionId': checkoutRequestId });

      if (order) {
        order.payment.status = 'failed';
        order.payment.failureReason = stkCallback.ResultDesc;
        await order.save();

        logger.info(`M-Pesa payment failed for order: ${order._id}, Reason: ${stkCallback.ResultDesc}`);

        // Emit real-time notification to user
        const io = req.app.get('io');
        io.to(`user_${order.user}`).emit('payment_failed', {
          orderId: order._id,
          reason: stkCallback.ResultDesc
        });
      }
    }

    res.json({ ResultCode: 0, ResultDesc: 'Success' });

  } catch (error) {
    logger.error('M-Pesa callback error:', error);
    res.json({ ResultCode: 1, ResultDesc: 'Error processing callback' });
  }
});

// @desc    Check M-Pesa payment status
// @route   GET /api/payments/mpesa/status/:checkoutRequestId
// @access  Private
router.get('/mpesa/status/:checkoutRequestId', protect, async (req, res) => {
  try {
    const { checkoutRequestId } = req.params;

    // Get M-Pesa access token
    const accessToken = await getMpesaAccessToken();

    // Generate timestamp and password
    const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
    const password = Buffer.from(`${MPESA_CONFIG.shortCode}${MPESA_CONFIG.passkey}${timestamp}`).toString('base64');

    const queryData = {
      BusinessShortCode: MPESA_CONFIG.shortCode,
      Password: password,
      Timestamp: timestamp,
      CheckoutRequestID: checkoutRequestId
    };

    const url = MPESA_CONFIG.environment === 'production'
      ? 'https://api.safaricom.co.ke/mpesa/stkpushquery/v1/query'
      : 'https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query';

    const response = await axios.post(url, queryData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    res.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    logger.error('M-Pesa status check error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check payment status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
