import React, { useState } from 'react';
import {
  Container,
  Box,
  VStack,
  HStack,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  Input,
  Select,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiSearch, FiDownload, FiFilter } from 'react-icons/fi';

const PaymentReview = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterMethod, setFilterMethod] = useState('all');

  const payments = [
    {
      id: '1234',
      date: '2024-01-15',
      amount: '$1,234.56',
      method: 'Credit Card',
      status: 'completed',
      customer: '<PERSON>',
      reference: 'PAY-1234-ABCD',
    },
    // Add more payment records here
  ];

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <HStack justify="space-between">
          <Text fontSize="2xl" fontWeight="bold">Payment Review</Text>
          <Button
            leftIcon={<FiDownload />}
            colorScheme="green"
          >
            Export
          </Button>
        </HStack>

        <HStack spacing={4}>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <FiSearch color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder="Search payments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </InputGroup>
          <Select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            w="200px"
          >
            <option value="all">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
          </Select>
          <Select
            value={filterMethod}
            onChange={(e) => setFilterMethod(e.target.value)}
            w="200px"
          >
            <option value="all">All Methods</option>
            <option value="credit_card">Credit Card</option>
            <option value="paypal">PayPal</option>
            <option value="bank_transfer">Bank Transfer</option>
          </Select>
        </HStack>

        <Box
          bg={useColorModeValue('white', 'gray.800')}
          borderRadius="lg"
          shadow="sm"
          overflow="hidden"
        >
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Reference</Th>
                <Th>Date</Th>
                <Th>Customer</Th>
                <Th>Amount</Th>
                <Th>Method</Th>
                <Th>Status</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {payments.map((payment) => (
                <Tr key={payment.id}>
                  <Td>{payment.reference}</Td>
                  <Td>{payment.date}</Td>
                  <Td>{payment.customer}</Td>
                  <Td>{payment.amount}</Td>
                  <Td>{payment.method}</Td>
                  <Td>
                    <Badge
                      colorScheme={
                        payment.status === 'completed' ? 'green' :
                        payment.status === 'pending' ? 'yellow' : 'red'
                      }
                    >
                      {payment.status}
                    </Badge>
                  </Td>
                  <Td>
                    <Button size="sm" variant="ghost">
                      View Details
                    </Button>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      </VStack>
    </Container>
  );
};

export default PaymentReview;