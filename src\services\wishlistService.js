import create from 'zustand';
import { persist } from 'zustand/middleware';

const useWishlistStore = create(
  persist(
    (set, get) => ({
      items: [],

      toggleWishlist: (product) => {
        const items = get().items;
        const existingItem = items.find(item => item.id === product.id);

        if (existingItem) {
          set(state => ({
            items: state.items.filter(item => item.id !== product.id)
          }));
        } else {
          set(state => ({
            items: [...state.items, product]
          }));
        }
      },

      isInWishlist: (productId) => {
        const items = get().items;
        return items.some(item => item.id === productId);
      },

      clearWishlist: () => set({ items: [] })
    }),
    {
      name: 'wishlist-storage'
    }
  )
);

export default useWishlistStore;