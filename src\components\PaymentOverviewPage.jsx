import React from 'react';
import {
  Container,
  Box,
  SimpleGrid,
  VStack,
  HStack,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Icon,
  Button,
  Select,
  useColorModeValue,
} from '@chakra-ui/react';
import {
  FiCreditCard,
  FiTrendingUp,
  FiTrendingDown,
  FiCalendar,
  FiDollarSign,
  FiActivity,
} from 'react-icons/fi';
import { Link as RouterLink } from 'react-router-dom';

const StatCard = ({ label, value, trend, icon, color }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      p={6}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack spacing={4} mb={4}>
        <Icon as={icon} boxSize={6} color={`${color}.500`} />
        <Text fontSize="lg" fontWeight="medium">{label}</Text>
      </HStack>
      <Stat>
        <StatNumber fontSize="3xl">{value}</StatNumber>
        <StatHelpText>
          <StatArrow type={trend > 0 ? 'increase' : 'decrease'} />
          {Math.abs(trend)}% since last month
        </StatHelpText>
      </Stat>
    </Box>
  );
};

const PaymentOverviewPage = () => {
  const [timeRange, setTimeRange] = React.useState('month');
  const bgColor = useColorModeValue('gray.50', 'gray.900');

  const stats = [
    {
      label: 'Total Transactions',
      value: '$336,225',
      trend: 8.4,
      icon: FiDollarSign,
      color: 'blue'
    },
    {
      label: 'Success Rate',
      value: '98.5%',
      trend: 1.2,
      icon: FiActivity,
      color: 'green'
    },
    {
      label: 'Average Transaction',
      value: '$1,242',
      trend: -2.3,
      icon: FiCreditCard,
      color: 'purple'
    },
    {
      label: 'Daily Volume',
      value: '$12,450',
      trend: 5.6,
      icon: FiTrendingUp,
      color: 'orange'
    }
  ];

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <HStack justify="space-between">
          <Box>
            <Text fontSize="2xl" fontWeight="bold">Payment Overview</Text>
            <Text color="gray.500">Comprehensive view of your payment metrics</Text>
          </Box>
          <HStack spacing={4}>
            <Select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              w="200px"
            >
              <option value="day">Last 24 Hours</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="year">Last Year</option>
            </Select>
            <Button
              as={RouterLink}
              to="/payments/review"
              colorScheme="blue"
              leftIcon={<FiActivity />}
            >
              View Transactions
            </Button>
          </HStack>
        </HStack>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          {stats.map((stat, index) => (
            <StatCard key={index} {...stat} />
          ))}
        </SimpleGrid>

        {/* Add charts and additional metrics here */}
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
          <Box
            p={6}
            bg={useColorModeValue('white', 'gray.800')}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={useColorModeValue('gray.200', 'gray.700')}
            shadow="sm"
            height="400px"
          >
            <Text fontSize="lg" fontWeight="medium" mb={4}>Transaction Volume Trend</Text>
            {/* Add chart component here */}
          </Box>
          <Box
            p={6}
            bg={useColorModeValue('white', 'gray.800')}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={useColorModeValue('gray.200', 'gray.700')}
            shadow="sm"
            height="400px"
          >
            <Text fontSize="lg" fontWeight="medium" mb={4}>Payment Methods Distribution</Text>
            {/* Add chart component here */}
          </Box>
        </SimpleGrid>
      </VStack>
    </Container>
  );
};

export default PaymentOverviewPage;
