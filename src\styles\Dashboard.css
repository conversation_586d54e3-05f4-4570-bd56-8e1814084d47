/* Base styles */
:root {
  --header-height: 72px;
  --sidebar-width: 250px;
  --content-padding: 2rem;
  --primary-color: #3182ce;
  --secondary-color: #805ad5;
  --success-color: #38a169;
  --warning-color: #dd6b20;
  --danger-color: #e53e3e;
  --border-radius: 12px;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  --card-hover-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
}

* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  user-select: none;
}

/* Layout containers */
.dashboard-container {
  min-height: 100vh;
  width: 100%;
  animation: fadeIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom right, rgba(235, 244, 255, 0.5), rgba(235, 244, 255, 0));
}

.dashboard-content {
  flex: 1;
  padding: var(--content-padding);
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
}

/* Header styles */
.dashboard-header {
  height: var(--header-height);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 1rem;
}

.header-content {
  max-width: 1920px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

/* Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
  width: 100%;
}

/* Widget styles */
.widget-container {
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  height: 100%;
  box-shadow: var(--card-shadow);
  border: 1px solid rgba(226, 232, 240, 0.5);
  overflow: hidden;
}

.widget-container:hover {
  transform: translateY(-4px);
  box-shadow: var(--card-hover-shadow);
  border-color: rgba(160, 174, 192, 0.5);
}

.metric-card {
  height: 100%;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-speed) ease;
}

.metric-card:hover::before {
  opacity: 1;
}

/* Chart Container */
.chart-container {
  border-radius: var(--border-radius);
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

/* Component-specific styles */
.widget-container.weather-widget {
  background: linear-gradient(to bottom right, #4299e1, #3182ce);
  color: white;
}

.widget-container.task-manager {
  background: rgba(255, 255, 255, 0.8);
}

.widget-container.calendar-events {
  background: rgba(255, 255, 255, 0.8);
}

/* Stat cards with different accent colors */
.metric-card.revenue {
  border-left: 4px solid var(--primary-color);
}

.metric-card.users {
  border-left: 4px solid var(--success-color);
}

.metric-card.orders {
  border-left: 4px solid var(--secondary-color);
}

.metric-card.growth {
  border-left: 4px solid var(--warning-color);
}

/* Quick actions */
.quick-actions-wrapper {
  position: relative;
  margin-bottom: 1.5rem;
}

.quick-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.quick-actions::-webkit-scrollbar {
  display: none;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  animation: pulse 2s infinite;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Apply animations to specific components */
.dashboard-grid > *:nth-child(odd) {
  animation: slideInLeft 0.5s ease-out forwards;
}

.dashboard-grid > *:nth-child(even) {
  animation: slideInRight 0.5s ease-out forwards;
}

.dashboard-grid > *:first-child {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* Responsive breakpoints */
@media (max-width: 1536px) {
  .dashboard-content {
    padding: 1.5rem;
  }
}

@media (max-width: 1280px) {
  .dashboard-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .header-content {
    flex-direction: column;
    padding: 1rem 0;
  }

  .dashboard-header {
    height: auto;
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .dashboard-content {
    padding: 1rem;
  }

  .metric-card {
    padding: 1rem;
  }

  .header-user-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 640px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .quick-actions {
    padding: 0.5rem 0;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background-image: linear-gradient(to bottom right, rgba(26, 32, 44, 0.3), rgba(26, 32, 44, 0.1));
  }

  .widget-container {
    background: rgba(26, 32, 44, 0.8);
    border-color: rgba(45, 55, 72, 0.5);
  }

  .metric-card {
    background: rgba(26, 32, 44, 0.6);
  }

  .chart-container {
    background: rgba(26, 32, 44, 0.7);
    border-color: rgba(45, 55, 72, 0.5);
  }

  .widget-container.weather-widget {
    background: linear-gradient(to bottom right, #2c5282, #2b6cb0);
  }

  .widget-container.task-manager,
  .widget-container.calendar-events {
    background: rgba(26, 32, 44, 0.7);
  }

  /* Adjust text colors for dark mode */
  .dashboard-content {
    color: rgba(255, 255, 255, 0.92);
  }

  /* Adjust card shadows for dark mode */
  .widget-container {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  }

  .widget-container:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }
}



