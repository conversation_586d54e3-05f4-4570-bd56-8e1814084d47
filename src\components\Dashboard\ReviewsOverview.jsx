import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Badge,
  Avatar,
  Icon,
  Button,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiStar, FiTrendingUp, FiMessageSquare, FiArrowRight } from 'react-icons/fi';
import { Link as RouterLink } from 'react-router-dom';

const RatingBar = ({ rating, count, total }) => {
  const percentage = (count / total) * 100;
  return (
    <HStack spacing={2} w="full">
      <Text fontSize="sm" w="8">
        {rating}★
      </Text>
      <Progress
        value={percentage}
        size="sm"
        colorScheme={rating > 3 ? "green" : rating > 2 ? "yellow" : "red"}
        w="full"
        borderRadius="full"
      />
      <Text fontSize="sm" w="12">
        {count}
      </Text>
    </HStack>
  );
};

const ReviewsOverview = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const reviewStats = {
    average: 4.7,
    total: 1284,
    distribution: [
      { rating: 5, count: 824 },
      { rating: 4, count: 287 },
      { rating: 3, count: 98 },
      { rating: 2, count: 45 },
      { rating: 1, count: 30 },
    ],
    trend: 12.5,
    recentReviews: [
      {
        id: 1,
        author: 'Emma Wilson',
        avatar: 'https://bit.ly/sage-adebayo',
        rating: 5,
        comment: 'Exceptional quality and service! Highly recommended.',
        time: '10 minutes ago',
      },
      {
        id: 2,
        author: 'Michael Chen',
        avatar: 'https://bit.ly/ryan-florence',
        rating: 4,
        comment: 'Great product, fast delivery. Minor packaging issues.',
        time: '1 hour ago',
      },
    ],
  };

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack justify="space-between" mb={4}>
        <Text fontSize="lg" fontWeight="medium">Reviews Overview</Text>
        <Badge colorScheme="green" display="flex" alignItems="center" px={2} py={1}>
          <Icon as={FiTrendingUp} mr={1} />
          {reviewStats.trend}% ↑
        </Badge>
      </HStack>

      <HStack spacing={8} mb={6} align="flex-start">
        <VStack align="flex-start" spacing={1}>
          <HStack spacing={2}>
            <Text fontSize="3xl" fontWeight="bold">
              {reviewStats.average}
            </Text>
            <Icon as={FiStar} color="yellow.400" boxSize={6} />
          </HStack>
          <Text fontSize="sm" color="gray.500">
            {reviewStats.total} reviews
          </Text>
        </VStack>

        <VStack spacing={2} flex={1}>
          {reviewStats.distribution.reverse().map((item) => (
            <RatingBar
              key={item.rating}
              rating={item.rating}
              count={item.count}
              total={reviewStats.total}
            />
          ))}
        </VStack>
      </HStack>

      <Text fontSize="md" fontWeight="medium" mb={3}>
        Recent Reviews
      </Text>

      <VStack spacing={4} align="stretch">
        {reviewStats.recentReviews.map((review) => (
          <Box
            key={review.id}
            p={3}
            borderRadius="md"
            bg={useColorModeValue('gray.50', 'gray.700')}
          >
            <HStack justify="space-between" mb={2}>
              <HStack>
                <Avatar size="sm" name={review.author} src={review.avatar} />
                <Text fontWeight="medium">{review.author}</Text>
              </HStack>
              <HStack spacing={1}>
                {[...Array(5)].map((_, i) => (
                  <Icon
                    key={i}
                    as={FiStar}
                    color={i < review.rating ? "yellow.400" : "gray.300"}
                    boxSize={4}
                  />
                ))}
              </HStack>
            </HStack>
            <Text fontSize="sm" noOfLines={2} mb={1}>
              {review.comment}
            </Text>
            <Text fontSize="xs" color="gray.500">
              {review.time}
            </Text>
          </Box>
        ))}
      </VStack>

      <Button
        as={RouterLink}
        to="/reviews"
        rightIcon={<FiArrowRight />}
        variant="ghost"
        colorScheme="blue"
        size="sm"
        mt={4}
        width="full"
      >
        View All Reviews
      </Button>
    </Box>
  );
};

export default ReviewsOverview;