import { useState, useEffect, useRef } from 'react';
import {
  Box, Input, VStack, HStack, Text, Icon,
  Kbd, useColorModeValue, Portal, InputGroup, InputLeftElement
} from '@chakra-ui/react';
import { FiSearch, FiClock, FiStar, FiTrendingUp } from 'react-icons/fi';

const SmartSearch = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef(null);
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Sample search results
  const searchResults = [
    {
      icon: FiClock,
      title: "Monthly Revenue Report",
      subtitle: "Reports • Viewed 2h ago",
      url: "/dashboard"
    },
    {
      icon: FiStar,
      title: "Active Users Dashboard",
      subtitle: "Dashboards • Favorited",
      url: "/dashboard"
    },
    {
      icon: FiTrendingUp,
      title: "Performance Metrics",
      subtitle: "Analytics • Trending",
      url: "/dashboard"
    }
  ];

  // Filter results based on search query
  const filteredResults = searchQuery.trim() === ''
    ? searchResults
    : searchResults.filter(result =>
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
      );

  useEffect(() => {
    const handleKeyPress = (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(prev => !prev);
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev =>
            prev < filteredResults.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
          break;
        case 'Enter':
          if (selectedIndex >= 0 && selectedIndex < filteredResults.length) {
            // Navigate to the selected result
            console.log(`Navigating to: ${filteredResults[selectedIndex].url}`);
            setIsOpen(false);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredResults]);

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  return (
    <>
      <Box position="relative" w="full" maxW="600px" mx="auto">
        <HStack
          onClick={() => setIsOpen(true)}
          cursor="pointer"
          p={2}
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
          _hover={{ borderColor: 'blue.500' }}
        >
          <Icon as={FiSearch} />
          <Text color="gray.500">Quick Search</Text>
          <HStack ml="auto" spacing={1}>
            <Kbd>⌘</Kbd>
            <Kbd>K</Kbd>
          </HStack>
        </HStack>

        {isOpen && (
          <Portal>
            <Box
              position="fixed"
              top="0"
              left="0"
              w="100vw"
              h="100vh"
              bg="blackAlpha.600"
              zIndex={1000}
              onClick={() => setIsOpen(false)}
            >
              <VStack
                position="relative"
                top="20%"
                mx="auto"
                w="90%"
                maxW="600px"
                bg={bgColor}
                borderRadius="xl"
                boxShadow="xl"
                spacing={0}
                onClick={e => e.stopPropagation()}
              >
                <InputGroup size="lg">
                  <InputLeftElement pointerEvents="none">
                    <Icon as={FiSearch} color="gray.400" />
                  </InputLeftElement>
                  <Input
                    ref={inputRef}
                    placeholder="Search dashboards, reports, or type / for commands..."
                    size="lg"
                    border="none"
                    _focus={{ boxShadow: 'none' }}
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setSelectedIndex(-1);
                    }}
                  />
                </InputGroup>
                <VStack w="full" p={4} spacing={2} align="stretch">
                  <Text fontSize="sm" fontWeight="medium" color="gray.500">
                    {searchQuery.trim() === '' ? 'Recent Searches' : 'Search Results'}
                  </Text>
                  {filteredResults.length === 0 ? (
                    <Text color="gray.500" fontSize="sm" p={2}>
                      No results found. Try a different search term.
                    </Text>
                  ) : (
                    filteredResults.map((result, index) => (
                      <SearchResult
                        key={index}
                        icon={result.icon}
                        title={result.title}
                        subtitle={result.subtitle}
                        isSelected={index === selectedIndex}
                        onClick={() => {
                          console.log(`Clicked: ${result.url}`);
                          setIsOpen(false);
                        }}
                        onMouseEnter={() => setSelectedIndex(index)}
                      />
                    ))
                  )}
                </VStack>
              </VStack>
            </Box>
          </Portal>
        )}
      </Box>
    </>
  );
};

const SearchResult = ({ icon, title, subtitle, isSelected, onClick, onMouseEnter }) => {
  const hoverBg = useColorModeValue('gray.100', 'gray.700');
  const selectedBg = useColorModeValue('blue.50', 'blue.900');
  const selectedBorder = useColorModeValue('blue.500', 'blue.200');

  return (
    <HStack
      p={2}
      borderRadius="md"
      cursor="pointer"
      bg={isSelected ? selectedBg : 'transparent'}
      borderLeft={isSelected ? '2px solid' : 'none'}
      borderLeftColor={isSelected ? selectedBorder : 'transparent'}
      _hover={{ bg: isSelected ? selectedBg : hoverBg }}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      transition="all 0.2s"
    >
      <Icon as={icon} color={isSelected ? 'blue.500' : 'inherit'} />
      <VStack spacing={0} align="start">
        <Text fontWeight={isSelected ? 'bold' : 'medium'}>{title}</Text>
        <Text fontSize="sm" color="gray.500">{subtitle}</Text>
      </VStack>
    </HStack>
  );
};

export default SmartSearch;