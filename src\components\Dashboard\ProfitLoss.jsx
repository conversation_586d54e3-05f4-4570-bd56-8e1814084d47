import React from 'react';
import { Box, Heading, Stat, StatLabel, StatNumber, StatHelpText, StatArrow, Grid } from '@chakra-ui/react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const ProfitLoss = () => {
  const data = [
    { month: 'Jan', profit: 4000, loss: -2400 },
    { month: 'Feb', profit: 3000, loss: -1398 },
    { month: 'Mar', profit: 2000, loss: -9800 },
    { month: 'Apr', profit: 2780, loss: -3908 },
    { month: 'May', profit: 1890, loss: -4800 },
    { month: 'Jun', profit: 2390, loss: -3800 },
  ];

  return (
    <Box p={4} bg="white" borderRadius="lg" boxShadow="sm">
      <Heading size="md" mb={4}>Profit & Loss Overview</Heading>
      <Grid templateColumns="repeat(2, 1fr)" gap={4} mb={4}>
        <Stat>
          <StatLabel>Total Profit</StatLabel>
          <StatNumber color="green.500">$16,060</StatNumber>
          <StatHelpText>
            <StatArrow type="increase" />
            23.36%
          </StatHelpText>
        </Stat>
        <Stat>
          <StatLabel>Total Loss</StatLabel>
          <StatNumber color="red.500">$26,104</StatNumber>
          <StatHelpText>
            <StatArrow type="decrease" />
            12.05%
          </StatHelpText>
        </Stat>
      </Grid>
      <Box h="300px">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="profit" fill="#48BB78" />
            <Bar dataKey="loss" fill="#F56565" />
          </BarChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default ProfitLoss;