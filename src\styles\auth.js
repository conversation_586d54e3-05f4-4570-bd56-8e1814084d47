export const authStyles = {
  container: {
    minH: "100vh",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    p: { base: "4", md: "8" },
    bgGradient: "linear(to-br, blue.400, purple.500)",
    backgroundSize: "400% 400%",
    animation: "gradientBG 15s ease infinite"
  },
  formStack: {
    w: "100%",
    maxW: "400px",
    spacing: "6",
    bg: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(10px)",
    borderRadius: "xl",
    p: "8",
    boxShadow: "xl",
    border: "1px solid",
    borderColor: "whiteAlpha.200",
    _dark: {
      bg: "rgba(26, 32, 44, 0.05)",
    }
  },
  header: {
    fontSize: { base: "2xl", md: "3xl" },
    fontWeight: "bold",
    textAlign: "center",
    mb: "8",
    color: "white",
    letterSpacing: "tight"
  },
  submitButton: {
    w: "100%",
    mt: "6",
    size: "lg",
    bgGradient: "linear(to-r, blue.400, purple.500)",
    color: "white",
    _hover: {
      bgGradient: "linear(to-r, blue.500, purple.600)",
      transform: "translateY(-2px)",
      boxShadow: "lg"
    },
    _active: {
      transform: "translateY(0)"
    },
    transition: "all 0.2s ease"
  },
  linkText: {
    textAlign: "center",
    mt: "4",
    color: "whiteAlpha.900"
  }
}
