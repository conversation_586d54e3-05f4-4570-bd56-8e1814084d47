import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Box, VStack, HStack, Input, IconButton, useColorModeValue,
  Drawer, Drawer<PERSON>ontent, DrawerHeader, DrawerBody,
  Avatar, Text, Button, Badge, Tooltip, Menu,
  MenuButton, MenuList, MenuItem, Divider, Progress,
  useToast, useDisclosure
} from '@chakra-ui/react'
import {
  RiRobot2Fill, RiMagicFill, RiTranslate2, RiCodeSSlashFill,
  RiFileTextFill, RiSoundModuleFill, RiRestartLine
} from 'react-icons/ri'
import {
  FiSend, FiCopy, FiSave, FiTrash2, FiMoreVertical,
  FiDownload, FiShare2, FiBookmark
} from 'react-icons/fi'
import ReactMarkdown from 'react-markdown'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import confetti from 'canvas-confetti'

// Import AI service functions
import { getAIResponse, getSampleResponse } from '../services/aiService'

// Styles
import '../styles/AIAssistant.css';

function AIAssistant() {
  const [messages, setMessages] = useState([])
  const [input, setInput] = useState('')
  const [isThinking, setIsThinking] = useState(false)
  const [mode, setMode] = useState('chat') // chat, code, translate, explain
  const [savedResponses, setSavedResponses] = useState([])
  const [messageContext, setMessageContext] = useState([])
  const [confidence, setConfidence] = useState(100)
  const messagesEndRef = useRef(null)
  const toast = useToast()
  const { isOpen, onOpen, onClose } = useDisclosure()

  const bgColor = useColorModeValue('white', 'gray.800')
  const inputBg = useColorModeValue('gray.100', 'gray.700')

  const modes = [
    { id: 'chat', icon: RiRobot2Fill, label: 'Chat' },
    { id: 'code', icon: RiCodeSSlashFill, label: 'Code Assistant' },
    { id: 'translate', icon: RiTranslate2, label: 'Translator' },
    { id: 'explain', icon: RiFileTextFill, label: 'Explainer' },
    { id: 'magic', icon: RiMagicFill, label: 'AI Magic' }
  ]

  const suggestions = {
    chat: ["How can I help you?", "Tell me more about your project", "What would you like to know?"],
    code: ["Review this code", "Help me debug", "Optimize this function", "Generate test cases"],
    translate: ["Translate to Python", "Convert to TypeScript", "Explain this code"],
    explain: ["Explain this concept", "Break down this problem", "How does this work?"],
    magic: ["Improve this code", "Suggest better practices", "Make it more efficient"]
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const AIAvatar = ({ isThinking, confidence }) => (
    <Box position="relative">
      <motion.div
        animate={isThinking ? { rotate: 360 } : {}}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
      >
        <Avatar
          icon={<RiRobot2Fill size="1.5em" />}
          bg="blue.500"
          p={1}
          size="md"
        />
      </motion.div>
      {confidence < 100 && (
        <Badge
          position="absolute"
          bottom="-2"
          right="-2"
          colorScheme={confidence > 70 ? "green" : "yellow"}
          fontSize="xs"
        >
          {confidence}%
        </Badge>
      )}
    </Box>
  )

  const MessageBubble = ({ message, onSave, onCopy, onDelete }) => (
    <Box
      as={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`message-bubble ${message.sender}-message`}
      maxW="80%"
      position="relative"
    >
      <HStack align="start" spacing={3}>
        {message.sender === 'ai' && (
          <AIAvatar isThinking={false} confidence={message.confidence} />
        )}
        <Box
          bg={message.sender === 'ai' ? 'blue.500' : 'gray.100'}
          color={message.sender === 'ai' ? 'white' : 'black'}
          p={4}
          borderRadius="lg"
          position="relative"
        >
          <ReactMarkdown
            components={{
              code: ({ node, inline, className, children, ...props }) => {
                const match = /language-(\w+)/.exec(className || '')
                return !inline && match ? (
                  <SyntaxHighlighter
                    style={atomDark}
                    language={match[1]}
                    PreTag="div"
                    {...props}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                ) : (
                  <code className={className} {...props}>
                    {children}
                  </code>
                )
              }
            }}
          >
            {message.text}
          </ReactMarkdown>

          <HStack
            position="absolute"
            top={2}
            right={2}
            opacity={0}
            className="message-actions"
            transition="opacity 0.2s"
          >
            <IconButton
              size="xs"
              icon={<FiCopy />}
              onClick={() => onCopy(message.text)}
              aria-label="Copy"
            />
            <IconButton
              size="xs"
              icon={<FiSave />}
              onClick={() => onSave(message)}
              aria-label="Save"
            />
            <IconButton
              size="xs"
              icon={<FiTrash2 />}
              onClick={() => onDelete(message)}
              aria-label="Delete"
            />
          </HStack>
        </Box>
      </HStack>
      <Text fontSize="xs" color="gray.500" mt={1}>
        {new Date(message.timestamp).toLocaleTimeString()}
      </Text>
    </Box>
  )

  const handleSend = async () => {
    if (!input.trim()) return

    const userMessage = {
      text: input,
      sender: 'user',
      timestamp: new Date().toISOString(),
      mode
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsThinking(true)

    try {
      // Simulate AI thinking with progress
      let progress = 0
      const progressInterval = setInterval(() => {
        progress += Math.random() * 20
        setConfidence(Math.min(Math.round(progress), 100))
      }, 300)

      const response = await getAIResponse(input, mode, messageContext)
      clearInterval(progressInterval)

      // Trigger confetti for high-confidence responses
      if (response.confidence > 90) {
        confetti({
          particleCount: 100,
          spread: 70,
          origin: { y: 0.6 }
        })
      }

      const aiMessage = {
        text: response.text,
        sender: 'ai',
        timestamp: new Date().toISOString(),
        confidence: response.confidence,
        mode
      }

      setMessages(prev => [...prev, aiMessage])
      setMessageContext(prev => [...prev, userMessage, aiMessage])
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to get AI response. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true
      })
    } finally {
      setIsThinking(false)
      setConfidence(100)
    }
  }

  const handleSaveResponse = (message) => {
    setSavedResponses(prev => [...prev, message])
    toast({
      title: 'Response saved',
      status: 'success',
      duration: 2000
    })
  }

  const handleCopyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    toast({
      title: 'Copied to clipboard',
      status: 'success',
      duration: 2000
    })
  }

  const handleExport = () => {
    const exportData = {
      messages,
      timestamp: new Date().toISOString(),
      metadata: {
        mode,
        context: messageContext
      }
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-conversation-${new Date().toISOString()}.json`
    a.click()
  }

  return (
    <>
      <Tooltip label="AI Assistant" placement="left">
        <IconButton
          icon={<RiRobot2Fill />}
          colorScheme="blue"
          borderRadius="full"
          position="fixed"
          bottom="4"
          right="4"
          size="lg"
          onClick={onOpen}
          className="floating-button"
          zIndex={1000}
          boxShadow="0 4px 12px rgba(0, 0, 0, 0.2)"
          _hover={{ transform: 'scale(1.1)' }}
          transition="all 0.3s ease"
        />
      </Tooltip>

      <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="md" zIndex={1100}>
        <DrawerContent>
          <DrawerHeader borderBottomWidth="1px" bg="blue.500" color="white">
            <HStack justify="space-between">
              <HStack>
                <AIAvatar isThinking={false} confidence={100} />
                <Text>AI Assistant</Text>
              </HStack>
              <HStack>
                <IconButton
                  icon={<FiDownload />}
                  variant="ghost"
                  color="white"
                  onClick={handleExport}
                  aria-label="Export conversation"
                />
                <Menu>
                  <MenuButton
                    as={IconButton}
                    icon={<FiMoreVertical />}
                    variant="ghost"
                    color="white"
                  />
                  <MenuList>
                    <MenuItem icon={<FiBookmark />}>Saved Responses ({savedResponses.length})</MenuItem>
                    <MenuItem icon={<FiShare2 />}>Share Conversation</MenuItem>
                    <Divider />
                    <MenuItem icon={<RiRestartLine />} onClick={() => setMessages([])}>
                      Clear Conversation
                    </MenuItem>
                  </MenuList>
                </Menu>
              </HStack>
            </HStack>
          </DrawerHeader>

          <DrawerBody p={0}>
            <VStack h="full" spacing={0}>
              <Box p={4} w="full" bg={bgColor}>
                <HStack spacing={2} overflowX="auto" pb={2}>
                  {modes.map((m) => (
                    <Button
                      key={m.id}
                      size="sm"
                      leftIcon={<m.icon />}
                      variant={mode === m.id ? "solid" : "outline"}
                      colorScheme="blue"
                      onClick={() => setMode(m.id)}
                    >
                      {m.label}
                    </Button>
                  ))}
                </HStack>
              </Box>

              <Box flex="1" w="full" p={4} overflowY="auto" className="messages-container">
                <AnimatePresence>
                  {messages.map((message, index) => (
                    <MessageBubble
                      key={index}
                      message={message}
                      onSave={handleSaveResponse}
                      onCopy={handleCopyToClipboard}
                      onDelete={(msg) => setMessages(m => m.filter(item => item !== msg))}
                    />
                  ))}
                  {isThinking && (
                    <Box
                      as={motion.div}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="thinking-indicator"
                    >
                      <HStack spacing={3}>
                        <AIAvatar isThinking={true} confidence={confidence} />
                        <Box bg="gray.100" p={4} borderRadius="lg">
                          <Text>Thinking...</Text>
                          <Progress
                            size="xs"
                            isIndeterminate
                            colorScheme="blue"
                            mt={2}
                          />
                        </Box>
                      </HStack>
                    </Box>
                  )}
                </AnimatePresence>
                <div ref={messagesEndRef} />
              </Box>

              <Box p={4} w="full" bg={bgColor} borderTopWidth="1px">
                <VStack spacing={4}>
                  <HStack spacing={2} overflowX="auto" w="full">
                    {suggestions[mode].map((suggestion, index) => (
                      <Button
                        key={index}
                        size="sm"
                        variant="outline"
                        onClick={() => setInput(suggestion)}
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </HStack>
                  <HStack w="full">
                    <Input
                      placeholder={`Type your message in ${mode} mode...`}
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSend()}
                      bg={inputBg}
                    />
                    <IconButton
                      icon={<FiSend />}
                      colorScheme="blue"
                      onClick={handleSend}
                      isLoading={isThinking}
                      aria-label="Send message"
                    />
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  )
}

export default AIAssistant


