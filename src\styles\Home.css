.home-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 8s ease infinite;
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.product-card {
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 20px -10px rgba(0, 0, 0, 0.2);
}

.quick-action-button {
  transition: all 0.2s ease-in-out;
}

.quick-action-button:hover {
  transform: translateY(-2px);
}

.content-section {
  opacity: 0;
  animation: fadeIn 0.5s ease-in forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.commercial-banner {
  position: relative;
  overflow: hidden;
}

.commercial-banner img {
  transition: transform 0.3s ease-in-out;
}

.commercial-banner:hover img {
  transform: scale(1.05);
}

.stat-card {
  transition: all 0.3s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px -8px rgba(0, 0, 0, 0.3);
}

.activity-item {
  transition: all 0.2s ease-in-out;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.activity-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.metric-card {
  transition: all 0.3s ease-in-out;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px -8px rgba(0, 0, 0, 0.3);
}

.tab-panel {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.circular-progress {
  transition: all 0.3s ease-in-out;
}

.circular-progress:hover {
  filter: brightness(1.1);
}

.transaction-item {
  transition: all 0.2s ease-in-out;
}

.transaction-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateX(4px);
}


