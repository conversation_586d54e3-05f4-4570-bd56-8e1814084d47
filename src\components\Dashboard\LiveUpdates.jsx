import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Icon,
  Badge,
  useColorModeValue,
  Heading,
  Fade,
  Circle,
  Tooltip,
} from '@chakra-ui/react';
import {
  FiUsers,
  FiShoppingCart,
  FiAlertCircle,
  FiCheckCircle,
  FiActivity,
  FiDollarSign,
  FiLock,
  FiGlobe,
} from 'react-icons/fi';

const LiveUpdates = () => {
  const [updates, setUpdates] = useState([]);
  const scrollRef = useRef(null);
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const scrollBgColor = useColorModeValue('gray.50', 'gray.700');

  const getUpdateIcon = (type) => {
    const icons = {
      user: FiUsers,
      sale: FiShoppingCart,
      alert: FiAlertCircle,
      success: FiCheckCircle,
      activity: FiActivity,
      payment: FiDollarSign,
      security: FiLock,
      system: FiGlobe,
    };
    return icons[type] || FiActivity;
  };

  const getBadgeColor = (type) => {
    const colors = {
      user: 'blue',
      sale: 'green',
      alert: 'red',
      success: 'green',
      activity: 'purple',
      payment: 'yellow',
      security: 'orange',
      system: 'cyan',
    };
    return colors[type] || 'gray';
  };

  // Auto scroll to the latest update
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0;
    }
  }, [updates]);

  useEffect(() => {
    // Simulate different WebSocket connections
    const connections = [
      { url: 'wss://api/users/activity', type: 'user' },
      { url: 'wss://api/sales/live', type: 'sale' },
      { url: 'wss://api/system/alerts', type: 'alert' },
      { url: 'wss://api/security/events', type: 'security' },
    ];

    // Simulate WebSocket messages
    const simulateWebSocketMessage = () => {
      const types = ['user', 'sale', 'alert', 'success', 'activity', 'payment', 'security', 'system'];
      const messages = [
        'New user registration from London',
        'Sale completed - Premium Package',
        'System performance alert',
        'Backup completed successfully',
        'New activity detected',
        'Payment processed',
        'Security scan completed',
        'API endpoint updated',
      ];

      const randomType = types[Math.floor(Math.random() * types.length)];
      const randomMessage = messages[Math.floor(Math.random() * messages.length)];

      const newUpdate = {
        id: Date.now(),
        type: randomType,
        message: randomMessage,
        timestamp: new Date().toISOString(),
      };

      setUpdates(prev => [newUpdate, ...prev].slice(0, 50)); // Keep last 50 updates
    };

    // Simulate real-time updates
    const interval = setInterval(simulateWebSocketMessage, 3000);

    return () => {
      clearInterval(interval);
      // In a real application, you would close WebSocket connections here
    };
  }, []);

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      border="1px"
      borderColor={borderColor}
      height="600px" // Fixed height
      display="flex"
      flexDirection="column"
    >
      <HStack mb={4} justify="space-between" align="center">
        <Heading size="md">Live Updates</Heading>
        <Circle size="10px" bg="green.400" />
      </HStack>

      <Box
        ref={scrollRef}
        flex="1"
        overflow="auto"
        css={{
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            width: '6px',
            background: useColorModeValue('gray.100', 'gray.900'),
          },
          '&::-webkit-scrollbar-thumb': {
            background: useColorModeValue('gray.400', 'gray.700'),
            borderRadius: '24px',
          },
        }}
      >
        <VStack spacing={3} align="stretch">
          {updates.map((update) => (
            <Fade in={true} key={update.id}>
              <HStack
                p={3}
                bg={scrollBgColor}
                borderRadius="md"
                transition="all 0.2s"
                _hover={{ transform: 'translateX(8px)' }}
              >
                <Icon
                  as={getUpdateIcon(update.type)}
                  color={`${getBadgeColor(update.type)}.500`}
                  boxSize={5}
                />
                <Box flex="1">
                  <HStack justify="space-between">
                    <Text fontSize="sm" fontWeight="medium">
                      {update.message}
                    </Text>
                    <Tooltip label={new Date(update.timestamp).toLocaleString()}>
                      <Badge colorScheme={getBadgeColor(update.type)} variant="subtle">
                        {new Date(update.timestamp).toLocaleTimeString()}
                      </Badge>
                    </Tooltip>
                  </HStack>
                </Box>
              </HStack>
            </Fade>
          ))}
        </VStack>
      </Box>
    </Box>
  );
};

export default LiveUpdates;
