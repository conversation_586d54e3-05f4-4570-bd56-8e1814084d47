import winston from 'winston';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(__dirname, '../logs/error.log'),
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(__dirname, '../logs/combined.log'),
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  }),
];

// Create logger instance
export const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
  // Don't exit on handled exceptions
  exitOnError: false,
});

// Create logs directory if it doesn't exist
import fs from 'fs';
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// HTTP request logger middleware
export const httpLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const message = `${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`;
    
    if (res.statusCode >= 400) {
      logger.error(message);
    } else {
      logger.http(message);
    }
  });
  
  next();
};

// Database logger
export const dbLogger = {
  info: (message, meta = {}) => {
    logger.info(`[DATABASE] ${message}`, meta);
  },
  error: (message, error = {}) => {
    logger.error(`[DATABASE] ${message}`, { error: error.message, stack: error.stack });
  },
  warn: (message, meta = {}) => {
    logger.warn(`[DATABASE] ${message}`, meta);
  }
};

// Payment logger
export const paymentLogger = {
  info: (message, meta = {}) => {
    logger.info(`[PAYMENT] ${message}`, meta);
  },
  error: (message, error = {}) => {
    logger.error(`[PAYMENT] ${message}`, { error: error.message, stack: error.stack });
  },
  warn: (message, meta = {}) => {
    logger.warn(`[PAYMENT] ${message}`, meta);
  }
};

// Auth logger
export const authLogger = {
  info: (message, meta = {}) => {
    logger.info(`[AUTH] ${message}`, meta);
  },
  error: (message, error = {}) => {
    logger.error(`[AUTH] ${message}`, { error: error.message, stack: error.stack });
  },
  warn: (message, meta = {}) => {
    logger.warn(`[AUTH] ${message}`, meta);
  }
};

// Security logger for suspicious activities
export const securityLogger = {
  warn: (message, meta = {}) => {
    logger.warn(`[SECURITY] ${message}`, {
      ...meta,
      timestamp: new Date().toISOString(),
      severity: 'warning'
    });
  },
  error: (message, meta = {}) => {
    logger.error(`[SECURITY] ${message}`, {
      ...meta,
      timestamp: new Date().toISOString(),
      severity: 'critical'
    });
  }
};

// Performance logger
export const performanceLogger = {
  info: (operation, duration, meta = {}) => {
    logger.info(`[PERFORMANCE] ${operation} completed in ${duration}ms`, meta);
  },
  warn: (operation, duration, meta = {}) => {
    logger.warn(`[PERFORMANCE] ${operation} took ${duration}ms (slow)`, meta);
  }
};

// Email logger
export const emailLogger = {
  info: (message, meta = {}) => {
    logger.info(`[EMAIL] ${message}`, meta);
  },
  error: (message, error = {}) => {
    logger.error(`[EMAIL] ${message}`, { error: error.message, stack: error.stack });
  }
};

export default logger;
