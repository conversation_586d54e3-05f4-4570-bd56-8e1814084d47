import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Image,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon,
  useToast,
  Skeleton,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Badge,
  useColorModeValue,
  SimpleGrid,
} from '@chakra-ui/react';
import { FiStar, FiShoppingCart, FiHeart, FiArrowLeft } from 'react-icons/fi';
import { productService } from '../services/productService';
import { useCart } from '../context/CartContext';
import useWishlistStore from '../services/wishlistService';

const ProductDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  const { addToCart } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlistStore();

  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);

  const cardBg = useColorModeValue('white', 'gray.700');

  useEffect(() => {
    fetchProductData();
  }, [id]);

  const fetchProductData = async () => {
    try {
      setIsLoading(true);
      const data = await productService.getProductById(id);
      if (data) {
        setProduct(data);
        if (data.variants && data.variants.length > 0) {
          setSelectedVariant(data.variants[0]);
        }
      } else {
        toast({
          title: 'Product not found',
          status: 'error',
          duration: 5000,
        });
        navigate('/products');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      toast({
        title: 'Error',
        description: 'Failed to load product details',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (product?.variants?.length > 0) {
      setSelectedVariant(product.variants[0]);
    }
  }, [product]);

  const currentPrice = selectedVariant ? selectedVariant.price : product?.price;

  const handleAddToCart = () => {
    if (!product) return;

    const itemToAdd = {
      id: product.id,
      name: product.name,
      image: product.image,
      quantity: quantity,
      price: selectedVariant ? selectedVariant.price : product.price,
      variant: selectedVariant ? {
        id: selectedVariant.id,
        name: selectedVariant.name,
        price: selectedVariant.price
      } : null
    };

    addToCart(itemToAdd);
    toast({
      title: 'Added to cart',
      description: `${quantity} ${quantity > 1 ? 'items' : 'item'} added`,
      status: 'success',
      duration: 2000,
    });
  };

  const handleBuyNow = () => {
    handleAddToCart();
    navigate('/cart');  // Changed to navigate to cart instead of place-order
  };

  const isOutOfStock = selectedVariant
    ? !selectedVariant.inStock
    : !product?.inStock;

  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8}>
          <Skeleton height="400px" width="100%" />
          <Skeleton height="200px" width="100%" />
        </VStack>
      </Container>
    );
  }

  if (!product) return null;

  return (
    <Container maxW="container.xl" py={8}>
      <Button
        leftIcon={<FiArrowLeft />}
        variant="ghost"
        mb={6}
        onClick={() => navigate('/products')}
      >
        Back to Products
      </Button>

      <Box display="flex" flexDirection={{ base: 'column', md: 'row' }} gap={8}>
        <Box flex={1}>
          <Image
            src={product.image}
            alt={product.name}
            borderRadius="lg"
            width="100%"
            height="400px"
            objectFit="cover"
          />
        </Box>

        <VStack flex={1} align="start" spacing={6}>
          <Box>
            <Badge colorScheme="green" mb={2}>In Stock</Badge>
            <Heading size="xl">{product.name}</Heading>
            <HStack mt={2}>
              <Icon as={FiStar} color="yellow.400" />
              <Text>{product.rating} Rating</Text>
              <Text color="gray.500">({product.sales} sold)</Text>
            </HStack>
          </Box>

          {product?.variants && (
            <Box width="100%">
              <Text fontWeight="bold" mb={2}>Available Variants</Text>
              <SimpleGrid columns={2} spacing={2}>
                {product.variants.map((variant) => (
                  <Button
                    key={variant.id}
                    size="md"
                    variant={selectedVariant?.id === variant.id ? 'solid' : 'outline'}
                    colorScheme="blue"
                    isDisabled={!variant.inStock}
                    onClick={() => setSelectedVariant(variant)}
                    width="100%"
                  >
                    <VStack spacing={1}>
                      <Text>{variant.name}</Text>
                      <Text fontSize="sm" color={selectedVariant?.id === variant.id ? 'white' : 'blue.500'}>
                        ${variant.price}
                      </Text>
                      {!variant.inStock && (
                        <Badge colorScheme="red">Out of Stock</Badge>
                      )}
                    </VStack>
                  </Button>
                ))}
              </SimpleGrid>
            </Box>
          )}

          <Text fontSize="3xl" fontWeight="bold" color="blue.500">
            ${currentPrice}
          </Text>

          <Box>
            <Text fontWeight="bold" mb={2}>Quantity</Text>
            <NumberInput
              defaultValue={1}
              min={1}
              max={99}
              onChange={(_, value) => setQuantity(parseInt(value))}
            >
              <NumberInputField />
              <NumberInputStepper>
                <NumberIncrementStepper />
                <NumberDecrementStepper />
              </NumberInputStepper>
            </NumberInput>
          </Box>

          <HStack spacing={4} width="100%">
            <Button
              leftIcon={<FiShoppingCart />}
              colorScheme="blue"
              size="lg"
              flex={1}
              onClick={handleAddToCart}
              isDisabled={isOutOfStock}
            >
              Add to Cart
            </Button>
            <Button
              colorScheme="green"
              size="lg"
              flex={1}
              onClick={handleBuyNow}
              isDisabled={isOutOfStock}
            >
              Buy Now
            </Button>
            <Button
              leftIcon={<FiHeart />}
              variant={isInWishlist(id) ? "solid" : "outline"}
              colorScheme={isInWishlist(id) ? "red" : "blue"}
              size="lg"
              onClick={() => {
                toggleWishlist(product);
                toast({
                  title: isInWishlist(id) ? 'Removed from wishlist' : 'Added to wishlist',
                  status: 'success',
                  duration: 2000,
                });
              }}
            >
              {isInWishlist(id) ? 'In Wishlist' : 'Wishlist'}
            </Button>
          </HStack>
        </VStack>
      </Box>

      <Box mt={12}>
        <Tabs>
          <TabList>
            <Tab>Description</Tab>
            <Tab>Specifications</Tab>
            <Tab>Reviews</Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <Text>{product.description}</Text>
            </TabPanel>
            <TabPanel>
              <VStack align="stretch" spacing={4}>
                {product.specifications?.map((spec, index) => (
                  <HStack key={index} justify="space-between">
                    <Text fontWeight="bold">{spec.name}</Text>
                    <Text>{spec.value}</Text>
                  </HStack>
                ))}
              </VStack>
            </TabPanel>
            <TabPanel>
              <Text>Reviews coming soon...</Text>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
    </Container>
  );
};

export default ProductDetails;




