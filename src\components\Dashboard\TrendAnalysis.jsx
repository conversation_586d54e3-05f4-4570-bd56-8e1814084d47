import React from 'react';
import { Box, Heading, HStack, Tag, Text, VStack } from '@chakra-ui/react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const TrendAnalysis = () => {
  const data = [
    { name: 'Week 1', users: 4000, sales: 2400, engagement: 2400 },
    { name: 'Week 2', users: 3000, sales: 1398, engagement: 2210 },
    { name: 'Week 3', users: 2000, sales: 9800, engagement: 2290 },
    { name: 'Week 4', users: 2780, sales: 3908, engagement: 2000 },
  ];

  const trends = [
    { label: 'Rising', trend: 'User Engagement', color: 'green' },
    { label: 'Trending', trend: 'Mobile Usage', color: 'blue' },
    { label: 'Declining', trend: 'Desktop Usage', color: 'red' },
  ];

  return (
    <Box p={4} bg="white" borderRadius="lg" boxShadow="sm">
      <Heading size="md" mb={4}>Trend Analysis</Heading>
      <HStack spacing={4} mb={4} wrap="wrap">
        {trends.map((item, index) => (
          <Tag key={index} colorScheme={item.color} size="md">
            {item.label}: {item.trend}
          </Tag>
        ))}
      </HStack>
      <Box h="300px">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Area type="monotone" dataKey="users" stackId="1" stroke="#8884d8" fill="#8884d8" />
            <Area type="monotone" dataKey="sales" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
            <Area type="monotone" dataKey="engagement" stackId="1" stroke="#ffc658" fill="#ffc658" />
          </AreaChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default TrendAnalysis;