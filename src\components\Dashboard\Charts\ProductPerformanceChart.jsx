import { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  useColorModeValue,
  Button,
  Icon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Progress,
  Select,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Image,
  Divider
} from '@chakra-ui/react';
import { 
  FiPackage, 
  FiTrendingUp, 
  FiTrendingDown, 
  FiDollarSign, 
  FiShoppingCart, 
  FiStar, 
  FiFilter 
} from 'react-icons/fi';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Sample data
const productData = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    image: 'https://via.placeholder.com/40',
    category: 'Electronics',
    price: 199.99,
    sales: 1245,
    revenue: 249000,
    rating: 4.8,
    stock: 78,
    trend: 12.3
  },
  {
    id: 2,
    name: 'Ultra HD Smart TV 55"',
    image: 'https://via.placeholder.com/40',
    category: 'Electronics',
    price: 699.99,
    sales: 532,
    revenue: 372400,
    rating: 4.5,
    stock: 23,
    trend: 8.7
  },
  {
    id: 3,
    name: 'Ergonomic Office Chair',
    image: 'https://via.placeholder.com/40',
    category: 'Furniture',
    price: 249.99,
    sales: 876,
    revenue: 219000,
    rating: 4.6,
    stock: 45,
    trend: 15.2
  },
  {
    id: 4,
    name: 'Professional DSLR Camera',
    image: 'https://via.placeholder.com/40',
    category: 'Photography',
    price: 1299.99,
    sales: 321,
    revenue: 417300,
    rating: 4.9,
    stock: 12,
    trend: -3.5
  },
  {
    id: 5,
    name: 'Stainless Steel Cookware Set',
    image: 'https://via.placeholder.com/40',
    category: 'Kitchen',
    price: 179.99,
    sales: 654,
    revenue: 117700,
    rating: 4.3,
    stock: 34,
    trend: 5.8
  },
  {
    id: 6,
    name: 'Smartphone Flagship Model',
    image: 'https://via.placeholder.com/40',
    category: 'Electronics',
    price: 999.99,
    sales: 987,
    revenue: 986900,
    rating: 4.7,
    stock: 56,
    trend: 9.4
  }
];

// Chart data
const generateChartData = (sortBy) => {
  const sorted = [...productData].sort((a, b) => b[sortBy] - a[sortBy]).slice(0, 5);
  return sorted.map(product => ({
    name: product.name.length > 15 ? product.name.substring(0, 15) + '...' : product.name,
    [sortBy]: sortBy === 'revenue' ? product[sortBy] / 1000 : product[sortBy]
  }));
};

const ProductPerformanceChart = () => {
  const [sortBy, setSortBy] = useState('sales');
  const [chartData, setChartData] = useState(generateChartData('sales'));
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const headerBg = useColorModeValue('gray.50', 'gray.700');
  
  const handleSortChange = (value) => {
    setSortBy(value);
    setChartData(generateChartData(value));
  };
  
  const getBarColor = () => {
    switch(sortBy) {
      case 'revenue': return useColorModeValue('#38A169', '#68D391'); // green.500, green.300
      case 'rating': return useColorModeValue('#D69E2E', '#F6E05E'); // yellow.500, yellow.300
      case 'trend': return useColorModeValue('#3182CE', '#63B3ED'); // blue.500, blue.300
      case 'sales':
      default: return useColorModeValue('#E53E3E', '#FC8181'); // red.500, red.300
    }
  };
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">
            <HStack>
              <Icon as={FiPackage} />
              <Text>Product Performance</Text>
            </HStack>
          </Heading>
          <Text color={textColor} fontSize="sm">
            Top performing products by {sortBy}
          </Text>
        </Box>
        <HStack>
          <Select 
            size="sm" 
            value={sortBy}
            onChange={(e) => handleSortChange(e.target.value)}
            width={{ base: '110px', md: '130px' }}
          >
            <option value="sales">By Sales</option>
            <option value="revenue">By Revenue</option>
            <option value="rating">By Rating</option>
            <option value="trend">By Trend</option>
          </Select>
          <Button size="sm" variant="ghost">
            <Icon as={FiFilter} />
          </Button>
        </HStack>
      </Flex>
      
      <Tabs variant="soft-rounded" colorScheme="blue" size="sm">
        <TabList mb={4}>
          <Tab>Chart View</Tab>
          <Tab>Table View</Tab>
        </TabList>
        <TabPanels>
          <TabPanel p={0}>
            <Box height={{ base: '250px', md: '300px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={chartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                  layout="vertical"
                >
                  <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                  <XAxis 
                    type="number" 
                    tick={{ fill: textColor }} 
                    tickFormatter={(value) => sortBy === 'revenue' ? `$${value}k` : value}
                  />
                  <YAxis 
                    dataKey="name" 
                    type="category" 
                    tick={{ fill: textColor }} 
                    width={150}
                  />
                  <RechartsTooltip
                    formatter={(value) => [
                      sortBy === 'revenue' 
                        ? `$${value}k` 
                        : sortBy === 'rating' 
                          ? `${value} stars` 
                          : sortBy === 'trend' 
                            ? `${value}%` 
                            : value,
                      sortBy.charAt(0).toUpperCase() + sortBy.slice(1)
                    ]}
                    contentStyle={{ 
                      backgroundColor: bgColor, 
                      borderColor: borderColor,
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Bar 
                    dataKey={sortBy} 
                    fill={getBarColor()} 
                    radius={[0, 4, 4, 0]}
                    barSize={30}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </TabPanel>
          <TabPanel p={0}>
            <Box overflowX="auto">
              <Table variant="simple" size="sm">
                <Thead bg={headerBg}>
                  <Tr>
                    <Th>Product</Th>
                    <Th isNumeric>Price</Th>
                    <Th isNumeric>Sales</Th>
                    <Th isNumeric>Revenue</Th>
                    <Th isNumeric>Rating</Th>
                    <Th isNumeric>Stock</Th>
                    <Th isNumeric>Trend</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {productData.map(product => (
                    <Tr key={product.id} _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}>
                      <Td>
                        <HStack>
                          <Image 
                            src={product.image} 
                            alt={product.name} 
                            boxSize="30px" 
                            borderRadius="md"
                            objectFit="cover"
                          />
                          <Box>
                            <Text fontWeight="medium" fontSize="sm">{product.name}</Text>
                            <Text fontSize="xs" color={textColor}>{product.category}</Text>
                          </Box>
                        </HStack>
                      </Td>
                      <Td isNumeric>${product.price}</Td>
                      <Td isNumeric>{product.sales}</Td>
                      <Td isNumeric>${(product.revenue / 1000).toFixed(1)}k</Td>
                      <Td isNumeric>
                        <HStack justify="flex-end">
                          <Icon as={FiStar} color="yellow.400" />
                          <Text>{product.rating}</Text>
                        </HStack>
                      </Td>
                      <Td isNumeric>
                        <Badge colorScheme={product.stock < 20 ? 'red' : product.stock < 50 ? 'yellow' : 'green'}>
                          {product.stock}
                        </Badge>
                      </Td>
                      <Td isNumeric>
                        <HStack justify="flex-end">
                          <Icon 
                            as={product.trend >= 0 ? FiTrendingUp : FiTrendingDown} 
                            color={product.trend >= 0 ? 'green.500' : 'red.500'} 
                          />
                          <Text color={product.trend >= 0 ? 'green.500' : 'red.500'}>
                            {product.trend >= 0 ? '+' : ''}{product.trend}%
                          </Text>
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>
      
      <Divider my={4} />
      
      <Flex justify="space-between" align="center">
        <Text fontSize="xs" color={textColor}>
          Showing top {productData.length} of {productData.length * 3} products
        </Text>
        <Button size="sm" variant="link" colorScheme="blue">
          View All Products
        </Button>
      </Flex>
    </Box>
  );
};

export default ProductPerformanceChart;
