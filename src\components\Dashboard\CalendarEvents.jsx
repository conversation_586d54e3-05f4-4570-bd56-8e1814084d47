import { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  HStack,
  VStack,
  useColorModeValue,
  Button,
  Icon,
  Badge,
  Divider,
  Avatar,
  AvatarGroup,
  IconButton,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem
} from '@chakra-ui/react';
import { 
  FiCalendar, 
  FiClock, 
  FiPlus, 
  FiMoreVertical, 
  FiUsers, 
  FiVideo,
  FiMapPin,
  FiCheckCircle,
  FiAlertCircle,
  FiCoffee,
  FiFileText,
  FiMessageSquare
} from 'react-icons/fi';

// Sample data
const today = new Date();
const tomorrow = new Date(today);
tomorrow.setDate(tomorrow.getDate() + 1);
const dayAfterTomorrow = new Date(today);
dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

const events = [
  {
    id: 1,
    title: 'Weekly Team Meeting',
    date: today,
    startTime: '10:00 AM',
    endTime: '11:30 AM',
    type: 'meeting',
    location: 'Conference Room A',
    attendees: [
      { name: '<PERSON>', avatar: 'https://via.placeholder.com/150' },
      { name: '<PERSON>', avatar: 'https://via.placeholder.com/150' },
      { name: '<PERSON>', avatar: 'https://via.placeholder.com/150' },
      { name: 'Emily Davis', avatar: 'https://via.placeholder.com/150' },
      { name: 'Michael Wilson', avatar: 'https://via.placeholder.com/150' }
    ],
    isVirtual: true,
    status: 'upcoming'
  },
  {
    id: 2,
    title: 'Product Launch Planning',
    date: today,
    startTime: '2:00 PM',
    endTime: '3:30 PM',
    type: 'planning',
    location: 'Meeting Room B',
    attendees: [
      { name: 'John Doe', avatar: 'https://via.placeholder.com/150' },
      { name: 'Jane Smith', avatar: 'https://via.placeholder.com/150' },
      { name: 'Emily Davis', avatar: 'https://via.placeholder.com/150' }
    ],
    isVirtual: false,
    status: 'upcoming'
  },
  {
    id: 3,
    title: 'Client Presentation',
    date: tomorrow,
    startTime: '11:00 AM',
    endTime: '12:00 PM',
    type: 'presentation',
    location: 'Main Conference Room',
    attendees: [
      { name: 'John Doe', avatar: 'https://via.placeholder.com/150' },
      { name: 'Jane Smith', avatar: 'https://via.placeholder.com/150' },
      { name: 'Robert Johnson', avatar: 'https://via.placeholder.com/150' }
    ],
    isVirtual: true,
    status: 'upcoming'
  },
  {
    id: 4,
    title: 'Marketing Strategy Review',
    date: tomorrow,
    startTime: '3:00 PM',
    endTime: '4:30 PM',
    type: 'review',
    location: 'Marketing Department',
    attendees: [
      { name: 'Jane Smith', avatar: 'https://via.placeholder.com/150' },
      { name: 'Emily Davis', avatar: 'https://via.placeholder.com/150' },
      { name: 'Michael Wilson', avatar: 'https://via.placeholder.com/150' }
    ],
    isVirtual: false,
    status: 'upcoming'
  },
  {
    id: 5,
    title: 'Quarterly Budget Review',
    date: dayAfterTomorrow,
    startTime: '9:00 AM',
    endTime: '11:00 AM',
    type: 'finance',
    location: 'Finance Department',
    attendees: [
      { name: 'John Doe', avatar: 'https://via.placeholder.com/150' },
      { name: 'Robert Johnson', avatar: 'https://via.placeholder.com/150' },
      { name: 'Michael Wilson', avatar: 'https://via.placeholder.com/150' }
    ],
    isVirtual: false,
    status: 'upcoming'
  }
];

const getEventIcon = (type) => {
  switch(type) {
    case 'meeting': return FiUsers;
    case 'planning': return FiFileText;
    case 'presentation': return FiVideo;
    case 'review': return FiCheckCircle;
    case 'finance': return FiAlertCircle;
    default: return FiCalendar;
  }
};

const getEventColor = (type) => {
  switch(type) {
    case 'meeting': return 'blue';
    case 'planning': return 'green';
    case 'presentation': return 'purple';
    case 'review': return 'orange';
    case 'finance': return 'red';
    default: return 'gray';
  }
};

const formatDate = (date) => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow';
  } else {
    return date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' });
  }
};

const CalendarEvents = () => {
  const [filter, setFilter] = useState('all');
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const sectionBg = useColorModeValue('gray.50', 'gray.700');
  
  // Group events by date
  const groupedEvents = events.reduce((acc, event) => {
    const dateString = event.date.toDateString();
    if (!acc[dateString]) {
      acc[dateString] = [];
    }
    acc[dateString].push(event);
    return acc;
  }, {});
  
  // Filter events if needed
  const filteredEvents = filter === 'all' 
    ? groupedEvents 
    : Object.keys(groupedEvents).reduce((acc, date) => {
        acc[date] = groupedEvents[date].filter(event => 
          filter === 'virtual' ? event.isVirtual : !event.isVirtual
        );
        if (acc[date].length === 0) delete acc[date];
        return acc;
      }, {});
  
  return (
    <Box
      bg={bgColor}
      borderRadius="lg"
      boxShadow="sm"
      p={4}
      height="100%"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Box>
          <Heading size="md">
            <HStack>
              <Icon as={FiCalendar} />
              <Text>Upcoming Events</Text>
            </HStack>
          </Heading>
          <Text color={textColor} fontSize="sm">
            Your schedule for the next few days
          </Text>
        </Box>
        <HStack>
          <Menu>
            <MenuButton as={Button} size="sm" variant="outline" rightIcon={<FiMoreVertical />}>
              {filter === 'all' ? 'All Events' : filter === 'virtual' ? 'Virtual' : 'In-Person'}
            </MenuButton>
            <MenuList>
              <MenuItem onClick={() => setFilter('all')}>All Events</MenuItem>
              <MenuItem onClick={() => setFilter('virtual')}>Virtual Only</MenuItem>
              <MenuItem onClick={() => setFilter('in-person')}>In-Person Only</MenuItem>
            </MenuList>
          </Menu>
          <Tooltip label="Add new event">
            <IconButton
              icon={<FiPlus />}
              aria-label="Add event"
              colorScheme="blue"
              size="sm"
            />
          </Tooltip>
        </HStack>
      </Flex>
      
      <VStack spacing={4} align="stretch">
        {Object.keys(filteredEvents).length === 0 ? (
          <Box textAlign="center" py={8}>
            <Icon as={FiCalendar} boxSize={10} color={textColor} mb={4} />
            <Text>No events found</Text>
            <Button size="sm" leftIcon={<FiPlus />} colorScheme="blue" mt={4}>
              Add New Event
            </Button>
          </Box>
        ) : (
          Object.keys(filteredEvents).map(date => (
            <Box key={date}>
              <Flex 
                bg={sectionBg} 
                p={2} 
                borderRadius="md" 
                mb={2}
                justify="space-between"
                align="center"
              >
                <Text fontWeight="bold">{formatDate(new Date(date))}</Text>
                <Text fontSize="sm" color={textColor}>
                  {filteredEvents[date].length} event{filteredEvents[date].length !== 1 ? 's' : ''}
                </Text>
              </Flex>
              
              <VStack spacing={3} align="stretch">
                {filteredEvents[date].map(event => (
                  <Box 
                    key={event.id}
                    p={3}
                    borderRadius="md"
                    borderLeft="4px solid"
                    borderLeftColor={`${getEventColor(event.type)}.500`}
                    bg={useColorModeValue('white', 'gray.800')}
                    boxShadow="sm"
                    _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
                    transition="all 0.2s"
                  >
                    <Flex justify="space-between" align="flex-start">
                      <HStack align="flex-start" spacing={3}>
                        <Icon 
                          as={getEventIcon(event.type)} 
                          color={`${getEventColor(event.type)}.500`}
                          boxSize={5}
                          mt={1}
                        />
                        <Box>
                          <Text fontWeight="bold">{event.title}</Text>
                          <HStack fontSize="sm" color={textColor} mt={1}>
                            <Icon as={FiClock} boxSize={3} />
                            <Text>{event.startTime} - {event.endTime}</Text>
                          </HStack>
                          <HStack fontSize="sm" color={textColor} mt={1}>
                            <Icon as={FiMapPin} boxSize={3} />
                            <Text>{event.location}</Text>
                            {event.isVirtual && (
                              <Badge colorScheme="purple" fontSize="xs">
                                Virtual
                              </Badge>
                            )}
                          </HStack>
                        </Box>
                      </HStack>
                      <Menu>
                        <MenuButton 
                          as={IconButton}
                          icon={<FiMoreVertical />}
                          variant="ghost"
                          size="sm"
                          aria-label="More options"
                        />
                        <MenuList>
                          <MenuItem icon={<FiVideo />}>Join Meeting</MenuItem>
                          <MenuItem icon={<FiFileText />}>View Details</MenuItem>
                          <MenuItem icon={<FiMessageSquare />}>Send Message</MenuItem>
                          <MenuItem icon={<FiCoffee />}>Reschedule</MenuItem>
                        </MenuList>
                      </Menu>
                    </Flex>
                    
                    <Divider my={2} />
                    
                    <Flex justify="space-between" align="center">
                      <AvatarGroup size="sm" max={3}>
                        {event.attendees.map((attendee, index) => (
                          <Avatar 
                            key={index} 
                            name={attendee.name} 
                            src={attendee.avatar} 
                            size="sm"
                          />
                        ))}
                      </AvatarGroup>
                      <Text fontSize="xs" color={textColor}>
                        {event.attendees.length} attendee{event.attendees.length !== 1 ? 's' : ''}
                      </Text>
                    </Flex>
                  </Box>
                ))}
              </VStack>
            </Box>
          ))
        )}
      </VStack>
      
      <Divider my={4} />
      
      <Flex justify="center">
        <Button size="sm" variant="link" colorScheme="blue">
          View Full Calendar
        </Button>
      </Flex>
    </Box>
  );
};

export default CalendarEvents;
