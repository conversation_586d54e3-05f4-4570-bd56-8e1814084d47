import { useState } from 'react';
import {
  Box, Button, Icon, Tooltip, useColorModeValue,
  Text, useBreakpointValue, VStack, useToast,
  Modal, ModalOverlay, ModalContent, ModalHeader,
  ModalBody, ModalCloseButton, useDisclosure,
  Heading, Flex
} from '@chakra-ui/react';
import {
  FiPlus, FiDownload, FiRefreshCw, FiFilter,
  FiPieChart, FiUsers, FiDollarSign, FiTrendingUp
} from 'react-icons/fi';

const QuickAction = ({ icon, label, onClick }) => {
  const showLabel = useBreakpointValue({ base: false, md: true });

  return (
    <Tooltip label={!showLabel ? label : ''}>
      <Button
        variant="ghost"
        leftIcon={<Icon as={icon} />}
        onClick={onClick}
        size={useBreakpointValue({ base: "sm", md: "md" })}
      >
        {showLabel && label}
      </Button>
    </Tooltip>
  );
};

const QuickActionsToolbar = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const isMobile = useBreakpointValue({ base: true, md: false });
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [activeAction, setActiveAction] = useState('');

  const handleActionClick = (action) => {
    setActiveAction(action);
    onOpen();

    // Show toast for actions that don't need a modal
    if (action === 'Sync' || action === 'Filter') {
      toast({
        title: `${action} action triggered`,
        description: `The ${action.toLowerCase()} operation has been initiated.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const actions = [
    { icon: FiPlus, label: 'New Report', onClick: () => handleActionClick('New Report') },
    { icon: FiPieChart, label: 'Analytics', onClick: () => handleActionClick('Analytics') },
    { icon: FiUsers, label: 'Team', onClick: () => handleActionClick('Team') },
    { icon: FiDollarSign, label: 'Finance', onClick: () => handleActionClick('Finance') },
    { icon: FiTrendingUp, label: 'Performance', onClick: () => handleActionClick('Performance') },
    { icon: FiDownload, label: 'Export', onClick: () => handleActionClick('Export') },
    { icon: FiRefreshCw, label: 'Sync', onClick: () => handleActionClick('Sync') },
    { icon: FiFilter, label: 'Filter', onClick: () => handleActionClick('Filter') },
  ];

  return (
    <Box className="quick-actions-wrapper">
      <Box
        className="quick-actions"
        bg={bgColor}
        borderRadius="lg"
        boxShadow="sm"
      >
        {actions.map((action, index) => (
          <QuickAction key={index} {...action} />
        ))}
      </Box>

      {isMobile && (
        <Box
          position="absolute"
          right={0}
          top="50%"
          transform="translateY(-50%)"
          bg="linear-gradient(to left, var(--chakra-colors-white) 0%, transparent 100%)"
          _dark={{
            bg: "linear-gradient(to left, var(--chakra-colors-gray-800) 0%, transparent 100%)"
          }}
          w="40px"
          h="100%"
          pointerEvents="none"
        />
      )}

      {/* Action Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{activeAction}</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {activeAction === 'New Report' && (
              <VStack align="stretch" spacing={4}>
                <Heading size="md">Create a New Report</Heading>
                <Text>This feature will allow you to create custom reports based on your data.</Text>
                <Flex justify="flex-end">
                  <Button colorScheme="blue">Create Report</Button>
                </Flex>
              </VStack>
            )}

            {activeAction === 'Analytics' && (
              <VStack align="stretch" spacing={4}>
                <Heading size="md">Analytics Dashboard</Heading>
                <Text>View detailed analytics about your business performance.</Text>
                <Flex justify="flex-end">
                  <Button colorScheme="blue">View Analytics</Button>
                </Flex>
              </VStack>
            )}

            {activeAction === 'Team' && (
              <VStack align="stretch" spacing={4}>
                <Heading size="md">Team Management</Heading>
                <Text>Manage your team members and their permissions.</Text>
                <Flex justify="flex-end">
                  <Button colorScheme="blue">Manage Team</Button>
                </Flex>
              </VStack>
            )}

            {activeAction === 'Finance' && (
              <VStack align="stretch" spacing={4}>
                <Heading size="md">Financial Overview</Heading>
                <Text>View and manage your financial data and transactions.</Text>
                <Flex justify="flex-end">
                  <Button colorScheme="blue">View Finances</Button>
                </Flex>
              </VStack>
            )}

            {activeAction === 'Performance' && (
              <VStack align="stretch" spacing={4}>
                <Heading size="md">Performance Metrics</Heading>
                <Text>Track and analyze your business performance metrics.</Text>
                <Flex justify="flex-end">
                  <Button colorScheme="blue">View Metrics</Button>
                </Flex>
              </VStack>
            )}

            {activeAction === 'Export' && (
              <VStack align="stretch" spacing={4}>
                <Heading size="md">Export Data</Heading>
                <Text>Export your data in various formats for external use.</Text>
                <Flex justify="flex-end">
                  <Button colorScheme="blue">Export Data</Button>
                </Flex>
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default QuickActionsToolbar;

