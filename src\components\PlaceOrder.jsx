import {
  Box,
  Button,
  Container,
  Divider,
  FormControl,
  FormLabel,
  Grid,
  Heading,
  HStack,
  Input,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Text,
  VStack,
  useColorModeValue,
  useToast,
  Image,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiCreditCard, FiTruck, FiPackage } from 'react-icons/fi';

const PlaceOrder = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [paymentMethod, setPaymentMethod] = useState('credit');
  const [shippingMethod, setShippingMethod] = useState('standard');
  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Mock order data
  const orderItems = [
    {
      id: 1,
      name: 'Premium Headphones',
      price: 299.99,
      quantity: 1,
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
    },
    {
      id: 2,
      name: 'Smart Watch Pro',
      price: 399.99,
      quantity: 2,
      image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500',
    },
  ];

  const shippingRates = {
    standard: 9.99,
    express: 19.99,
    overnight: 29.99,
  };

  const calculateSubtotal = () => {
    return orderItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateTotal = () => {
    return calculateSubtotal() + shippingRates[shippingMethod];
  };

  const handlePlaceOrder = () => {
    toast({
      title: 'Order Placed Successfully!',
      description: `Order total: $${calculateTotal().toFixed(2)}`,
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
    navigate('/orders');
  };

  return (
    <Container maxW="container.xl" py={8}>
      <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
        <VStack spacing={8} align="stretch">
          {/* Shipping Information */}
          <Box bg={bgColor} p={6} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <Heading size="md" mb={4}>
              <HStack>
                <FiTruck />
                <Text>Shipping Information</Text>
              </HStack>
            </Heading>
            <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4}>
              <FormControl>
                <FormLabel>First Name</FormLabel>
                <Input placeholder="John" />
              </FormControl>
              <FormControl>
                <FormLabel>Last Name</FormLabel>
                <Input placeholder="Doe" />
              </FormControl>
              <FormControl gridColumn={{ md: 'span 2' }}>
                <FormLabel>Address</FormLabel>
                <Input placeholder="123 Main St" />
              </FormControl>
              <FormControl>
                <FormLabel>City</FormLabel>
                <Input placeholder="New York" />
              </FormControl>
              <FormControl>
                <FormLabel>State</FormLabel>
                <Select placeholder="Select state">
                  <option value="ny">New York</option>
                  <option value="ca">California</option>
                  <option value="tx">Texas</option>
                </Select>
              </FormControl>
              <FormControl>
                <FormLabel>ZIP Code</FormLabel>
                <Input placeholder="10001" />
              </FormControl>
              <FormControl>
                <FormLabel>Phone</FormLabel>
                <Input placeholder="(*************" />
              </FormControl>
            </Grid>
          </Box>

          {/* Payment Method */}
          <Box bg={bgColor} p={6} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <Heading size="md" mb={4}>
              <HStack>
                <FiCreditCard />
                <Text>Payment Method</Text>
              </HStack>
            </Heading>
            <RadioGroup value={paymentMethod} onChange={setPaymentMethod}>
              <Stack spacing={4}>
                <Radio value="credit">Credit Card</Radio>
                <Radio value="paypal">PayPal</Radio>
                <Radio value="bitcoin">Bitcoin</Radio>
              </Stack>
            </RadioGroup>
            {paymentMethod === 'credit' && (
              <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4} mt={4}>
                <FormControl gridColumn={{ md: 'span 2' }}>
                  <FormLabel>Card Number</FormLabel>
                  <Input placeholder="1234 5678 9012 3456" />
                </FormControl>
                <FormControl>
                  <FormLabel>Expiry Date</FormLabel>
                  <Input placeholder="MM/YY" />
                </FormControl>
                <FormControl>
                  <FormLabel>CVV</FormLabel>
                  <Input placeholder="123" type="password" />
                </FormControl>
              </Grid>
            )}
          </Box>

          {/* Shipping Method */}
          <Box bg={bgColor} p={6} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <Heading size="md" mb={4}>
              <HStack>
                <FiPackage />
                <Text>Shipping Method</Text>
              </HStack>
            </Heading>
            <RadioGroup value={shippingMethod} onChange={setShippingMethod}>
              <Stack spacing={4}>
                <Radio value="standard">
                  <HStack justify="space-between" width="100%">
                    <Text>Standard Shipping (3-5 business days)</Text>
                    <Text>${shippingRates.standard.toFixed(2)}</Text>
                  </HStack>
                </Radio>
                <Radio value="express">
                  <HStack justify="space-between" width="100%">
                    <Text>Express Shipping (2-3 business days)</Text>
                    <Text>${shippingRates.express.toFixed(2)}</Text>
                  </HStack>
                </Radio>
                <Radio value="overnight">
                  <HStack justify="space-between" width="100%">
                    <Text>Overnight Shipping (1 business day)</Text>
                    <Text>${shippingRates.overnight.toFixed(2)}</Text>
                  </HStack>
                </Radio>
              </Stack>
            </RadioGroup>
          </Box>
        </VStack>

        {/* Order Summary */}
        <Box bg={bgColor} p={6} borderRadius="lg" borderWidth="1px" borderColor={borderColor} height="fit-content">
          <Heading size="md" mb={4}>Order Summary</Heading>
          <VStack spacing={4} align="stretch">
            {orderItems.map((item) => (
              <HStack key={item.id} spacing={4}>
                <Image
                  src={item.image}
                  alt={item.name}
                  boxSize="50px"
                  objectFit="cover"
                  borderRadius="md"
                />
                <Box flex={1}>
                  <Text fontWeight="medium">{item.name}</Text>
                  <Text fontSize="sm" color="gray.500">
                    Quantity: {item.quantity}
                  </Text>
                </Box>
                <Text fontWeight="medium">
                  ${(item.price * item.quantity).toFixed(2)}
                </Text>
              </HStack>
            ))}
            <Divider />
            <HStack justify="space-between">
              <Text>Subtotal</Text>
              <Text>${calculateSubtotal().toFixed(2)}</Text>
            </HStack>
            <HStack justify="space-between">
              <Text>Shipping</Text>
              <Text>${shippingRates[shippingMethod].toFixed(2)}</Text>
            </HStack>
            <Divider />
            <HStack justify="space-between" fontWeight="bold">
              <Text>Total</Text>
              <Text>${calculateTotal().toFixed(2)}</Text>
            </HStack>
            <Button
              colorScheme="blue"
              size="lg"
              width="100%"
              onClick={handlePlaceOrder}
            >
              Place Order
            </Button>
          </VStack>
        </Box>
      </Grid>
    </Container>
  );
};

export default PlaceOrder;