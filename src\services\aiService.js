// Temporary mock functions until backend integration
export const getAIResponse = async (message) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  return "This is a mock response. Replace with actual API call.";
};

export const saveMessages = (messages) => {
  try {
    localStorage.setItem('chatMessages', JSON.stringify(messages));
  } catch (error) {
    console.error('Error saving messages:', error);
  }
};

export const loadMessages = () => {
  try {
    const saved = localStorage.getItem('chatMessages');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Error loading messages:', error);
    return [];
  }
};

export const getSampleResponse = () => {
  const responses = [
    "How can I help you today?",
    "I'm here to assist you.",
    "What would you like to know?",
  ];
  return responses[Math.floor(Math.random() * responses.length)];
};

