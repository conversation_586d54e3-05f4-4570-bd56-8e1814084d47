import React, { useState } from 'react';
import {
  Box,
  Container,
  HStack,
  VStack,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Button,
  Avatar,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Grid,
  GridItem,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';
import {
  FiSearch,
  FiFilter,
  FiDownload,
  FiUser,
  FiDollarSign,
  FiShoppingBag,
  FiClock,
} from 'react-icons/fi';

const CustomerDetails = () => {
  const [filterStatus, setFilterStatus] = useState('all');
  const bgColor = useColorModeValue('white', 'gray.800');

  const customers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      totalSpent: 1299.99,
      orders: 12,
      lastPurchase: '2023-11-15',
      status: 'active',
      avatar: 'https://bit.ly/sage-adebayo',
    },
    // Add more customer data here
  ];

  const customerStats = [
    {
      label: 'Total Customers',
      value: 1458,
      change: 12.5,
      icon: FiUser,
    },
    {
      label: 'Average Order Value',
      value: 125.99,
      change: 5.3,
      icon: FiDollarSign,
    },
    {
      label: 'Total Orders',
      value: 3567,
      change: 8.1,
      icon: FiShoppingBag,
    },
  ];

  return (
    <Container maxW="container.xl" py={8}>
      {/* Stats Section */}
      <Grid templateColumns="repeat(3, 1fr)" gap={6} mb={8}>
        {customerStats.map((stat, index) => (
          <GridItem key={index}>
            <Box p={6} bg={bgColor} borderRadius="lg" shadow="sm">
              <Stat>
                <HStack spacing={2}>
                  <Icon as={stat.icon} color="blue.500" boxSize={5} />
                  <StatLabel>{stat.label}</StatLabel>
                </HStack>
                <StatNumber>
                  {typeof stat.value === 'number' && stat.label.includes('Value')
                    ? `$${stat.value}`
                    : stat.value.toLocaleString()}
                </StatNumber>
                <StatHelpText>
                  <StatArrow type={stat.change > 0 ? 'increase' : 'decrease'} />
                  {Math.abs(stat.change)}%
                </StatHelpText>
              </Stat>
            </Box>
          </GridItem>
        ))}
      </Grid>

      {/* Filters and Search */}
      <HStack spacing={4} mb={6}>
        <InputGroup maxW="320px">
          <InputLeftElement pointerEvents="none">
            <Icon as={FiSearch} color="gray.400" />
          </InputLeftElement>
          <Input placeholder="Search customers..." />
        </InputGroup>
        <Select
          maxW="200px"
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
        >
          <option value="all">All Customers</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="new">New</option>
        </Select>
        <Button leftIcon={<FiFilter />} variant="outline">
          More Filters
        </Button>
        <Button leftIcon={<FiDownload />} variant="outline">
          Export
        </Button>
      </HStack>

      {/* Customer List */}
      <Box bg={bgColor} borderRadius="lg" shadow="sm" overflow="hidden">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Customer</Th>
              <Th>Status</Th>
              <Th isNumeric>Total Spent</Th>
              <Th isNumeric>Orders</Th>
              <Th>Last Purchase</Th>
            </Tr>
          </Thead>
          <Tbody>
            {customers.map((customer) => (
              <Tr key={customer.id}>
                <Td>
                  <HStack spacing={3}>
                    <Avatar size="sm" name={customer.name} src={customer.avatar} />
                    <Box>
                      <Text fontWeight="medium">{customer.name}</Text>
                      <Text fontSize="sm" color="gray.500">
                        {customer.email}
                      </Text>
                    </Box>
                  </HStack>
                </Td>
                <Td>
                  <Badge
                    colorScheme={customer.status === 'active' ? 'green' : 'gray'}
                  >
                    {customer.status.toUpperCase()}
                  </Badge>
                </Td>
                <Td isNumeric>${customer.totalSpent.toLocaleString()}</Td>
                <Td isNumeric>{customer.orders}</Td>
                <Td>
                  <HStack spacing={2}>
                    <Icon as={FiClock} color="gray.500" />
                    <Text>{new Date(customer.lastPurchase).toLocaleDateString()}</Text>
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    </Container>
  );
};

export default CustomerDetails;