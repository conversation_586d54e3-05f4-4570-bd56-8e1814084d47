import React from 'react';
import {
  Box,
  Text,
  HStack,
  VStack,
  Icon,
  Button,
  SimpleGrid,
  useColorModeValue,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
} from '@chakra-ui/react';
import { FiCreditCard, FiArrowRight } from 'react-icons/fi';
import { Link as RouterLink } from 'react-router-dom';

const PaymentOverview = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const paymentStats = [
    {
      label: 'Total Transactions',
      value: '$336,225',
      trend: 8.4,
      color: 'blue'
    },
    {
      label: 'Success Rate',
      value: '98.5%',
      trend: 1.2,
      color: 'green'
    },
    {
      label: 'Active Methods',
      value: '6',
      trend: 12.5,
      color: 'purple'
    }
  ];

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      shadow="sm"
    >
      <HStack justify="space-between" mb={4}>
        <HStack>
          <Icon as={FiCreditCard} boxSize={6} color="blue.500" />
          <Text fontSize="lg" fontWeight="medium">Payment Overview</Text>
        </HStack>
        <Button
          as={RouterLink}
          to="/payments/overview"
          rightIcon={<FiArrowRight />}
          colorScheme="blue"
          size="sm"
        >
          View All Payments
        </Button>
      </HStack>

      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
        {paymentStats.map((stat, index) => (
          <Box
            key={index}
            p={4}
            bg={useColorModeValue('gray.50', 'gray.700')}
            borderRadius="md"
          >
            <Stat>
              <StatLabel>{stat.label}</StatLabel>
              <StatNumber color={`${stat.color}.500`}>{stat.value}</StatNumber>
              <StatHelpText>
                <StatArrow type={stat.trend > 0 ? 'increase' : 'decrease'} />
                {stat.trend}%
              </StatHelpText>
            </Stat>
          </Box>
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default PaymentOverview;
