import React, { useState } from 'react';
import {
  Container,
  VStack,
  HStack,
  Box,
  Heading,
  Text,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Radio,
  RadioGroup,
  Stack,
  Divider,
  useColorModeValue,
  useToast,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../context/CartContext';

const Checkout = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { cart, getCartTotal, clearCart } = useCart();
  const [paymentMethod, setPaymentMethod] = useState('credit');
  const cardBg = useColorModeValue('white', 'gray.700');

  const handleSubmit = (e) => {
    e.preventDefault();
    // Simulate order processing
    toast({
      title: 'Order placed successfully',
      description: 'Thank you for your purchase!',
      status: 'success',
      duration: 5000,
    });
    clearCart();
    navigate('/dashboard');
  };

  if (cart.length === 0) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={4} align="center">
          <Heading size="lg">No items to checkout</Heading>
          <Button
            colorScheme="blue"
            onClick={() => navigate('/products')}
          >
            Continue Shopping
          </Button>
        </VStack>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      <Heading size="lg" mb={6}>Checkout</Heading>

      <Box display="flex" gap={8} flexDirection={{ base: 'column', lg: 'row' }}>
        <VStack flex={2} align="stretch" spacing={6}>
          <Box p={6} borderWidth="1px" borderRadius="lg" bg={cardBg}>
            <Heading size="md" mb={4}>Shipping Information</Heading>
            <form onSubmit={handleSubmit}>
              <VStack spacing={4}>
                <HStack spacing={4} width="100%">
                  <FormControl isRequired>
                    <FormLabel>First Name</FormLabel>
                    <Input placeholder="John" />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>Last Name</FormLabel>
                    <Input placeholder="Doe" />
                  </FormControl>
                </HStack>

                <FormControl isRequired>
                  <FormLabel>Email</FormLabel>
                  <Input type="email" placeholder="<EMAIL>" />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>Address</FormLabel>
                  <Input placeholder="123 Main St" />
                </FormControl>

                <HStack spacing={4} width="100%">
                  <FormControl isRequired>
                    <FormLabel>City</FormLabel>
                    <Input placeholder="City" />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>State</FormLabel>
                    <Select placeholder="Select state">
                      <option value="ca">California</option>
                      <option value="ny">New York</option>
                      <option value="tx">Texas</option>
                    </Select>
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>ZIP</FormLabel>
                    <Input placeholder="12345" />
                  </FormControl>
                </HStack>
              </VStack>
            </form>
          </Box>

          <Box p={6} borderWidth="1px" borderRadius="lg" bg={cardBg}>
            <Heading size="md" mb={4}>Payment Method</Heading>
            <RadioGroup onChange={setPaymentMethod} value={paymentMethod}>
              <Stack spacing={4}>
                <Radio value="credit">Credit Card</Radio>
                <Radio value="debit">Debit Card</Radio>
                <Radio value="paypal">PayPal</Radio>
              </Stack>
            </RadioGroup>
          </Box>
        </VStack>

        <VStack flex={1} align="stretch" spacing={4}>
          <Box p={6} borderWidth="1px" borderRadius="lg" bg={cardBg}>
            <Heading size="md" mb={4}>Order Summary</Heading>
            
            <VStack spacing={3} align="stretch">
              <HStack justify="space-between">
                <Text>Items ({cart.length})</Text>
                <Text fontWeight="bold">${getCartTotal().toFixed(2)}</Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text>Shipping</Text>
                <Text fontWeight="bold">Free</Text>
              </HStack>

              <Divider />

              <HStack justify="space-between">
                <Text fontWeight="bold">Total</Text>
                <Text fontSize="xl" fontWeight="bold" color="blue.500">
                  ${getCartTotal().toFixed(2)}
                </Text>
              </HStack>

              <Button
                colorScheme="blue"
                size="lg"
                width="100%"
                onClick={handleSubmit}
              >
                Place Order
              </Button>
            </VStack>
          </Box>

          <Button
            variant="outline"
            onClick={() => navigate('/cart')}
          >
            Back to Cart
          </Button>
        </VStack>
      </Box>
    </Container>
  );
};

export default Checkout;