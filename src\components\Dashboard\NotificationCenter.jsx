import { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverArrow,
  PopoverCloseButton,
  VStack,
  HStack,
  Text,
  Badge,
  Icon,
  Divider,
  Button,
  useColorModeValue,
  Flex,
  Circle,
  Tooltip
} from '@chakra-ui/react';
import {
  FiBell,
  FiInfo,
  FiAlertCircle,
  FiCheckCircle,
  FiX,
  FiSettings,
  FiEye
} from 'react-icons/fi';

const NotificationItem = ({ notification, onMarkAsRead, onDelete }) => {
  const bgColor = useColorModeValue('gray.50', 'gray.700');
  
  const getIcon = () => {
    switch (notification.type) {
      case 'info':
        return FiInfo;
      case 'alert':
        return FiAlertCircle;
      case 'success':
        return FiCheckCircle;
      default:
        return FiInfo;
    }
  };
  
  const getColor = () => {
    switch (notification.type) {
      case 'info':
        return 'blue';
      case 'alert':
        return 'red';
      case 'success':
        return 'green';
      default:
        return 'blue';
    }
  };

  return (
    <Box
      p={3}
      bg={notification.read ? 'transparent' : bgColor}
      borderRadius="md"
      position="relative"
      transition="all 0.2s"
      _hover={{ bg: bgColor }}
    >
      <HStack spacing={3} align="flex-start">
        <Circle size="8" bg={`${getColor()}.100`} color={`${getColor()}.500`}>
          <Icon as={getIcon()} />
        </Circle>
        <Box flex="1">
          <Text fontWeight={notification.read ? 'normal' : 'bold'} fontSize="sm">
            {notification.title}
          </Text>
          <Text fontSize="xs" color="gray.500" mt={1}>
            {notification.message}
          </Text>
          <Text fontSize="xs" color="gray.500" mt={1}>
            {new Date(notification.timestamp).toLocaleString()}
          </Text>
        </Box>
      </HStack>
      
      <HStack position="absolute" top={1} right={1} opacity={0.7} _groupHover={{ opacity: 1 }}>
        {!notification.read && (
          <Tooltip label="Mark as read">
            <IconButton
              icon={<FiEye />}
              size="xs"
              variant="ghost"
              onClick={() => onMarkAsRead(notification.id)}
              aria-label="Mark as read"
            />
          </Tooltip>
        )}
        <Tooltip label="Delete">
          <IconButton
            icon={<FiX />}
            size="xs"
            variant="ghost"
            onClick={() => onDelete(notification.id)}
            aria-label="Delete notification"
          />
        </Tooltip>
      </HStack>
    </Box>
  );
};

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Generate mock notifications
  useEffect(() => {
    const mockNotifications = [
      {
        id: 1,
        type: 'info',
        title: 'New Feature Available',
        message: 'Check out our new analytics dashboard with improved metrics.',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        read: false
      },
      {
        id: 2,
        type: 'alert',
        title: 'System Maintenance',
        message: 'Scheduled maintenance in 2 hours. The system may be unavailable.',
        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
        read: false
      },
      {
        id: 3,
        type: 'success',
        title: 'Report Generated',
        message: 'Your monthly sales report has been successfully generated.',
        timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
        read: true
      },
      {
        id: 4,
        type: 'info',
        title: 'New User Registered',
        message: 'A new user has registered to your platform.',
        timestamp: new Date(Date.now() - 1000 * 60 * 360).toISOString(), // 6 hours ago
        read: true
      }
    ];
    
    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter(n => !n.read).length);
  }, []);
  
  const handleMarkAsRead = (id) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };
  
  const handleDelete = (id) => {
    const notification = notifications.find(n => n.id === id);
    setNotifications(prev => prev.filter(notification => notification.id !== id));
    if (!notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };
  
  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };
  
  return (
    <Popover placement="bottom-end">
      <PopoverTrigger>
        <Box position="relative" display="inline-block">
          <IconButton
            icon={<FiBell />}
            variant="ghost"
            aria-label="Notifications"
            position="relative"
          />
          {unreadCount > 0 && (
            <Circle
              size="18px"
              bg="red.500"
              color="white"
              fontSize="xs"
              fontWeight="bold"
              position="absolute"
              top="-5px"
              right="-5px"
            >
              {unreadCount}
            </Circle>
          )}
        </Box>
      </PopoverTrigger>
      <PopoverContent width="350px" maxH="500px" overflowY="auto">
        <PopoverArrow />
        <PopoverCloseButton />
        <PopoverHeader fontWeight="bold">
          <Flex justify="space-between" align="center">
            <Text>Notifications</Text>
            <HStack>
              <Tooltip label="Settings">
                <IconButton
                  icon={<FiSettings />}
                  size="sm"
                  variant="ghost"
                  aria-label="Notification settings"
                />
              </Tooltip>
              {unreadCount > 0 && (
                <Button size="xs" onClick={handleMarkAllAsRead}>
                  Mark all as read
                </Button>
              )}
            </HStack>
          </Flex>
        </PopoverHeader>
        <PopoverBody p={0}>
          {notifications.length === 0 ? (
            <Box p={4} textAlign="center">
              <Text color="gray.500">No notifications</Text>
            </Box>
          ) : (
            <VStack spacing={0} align="stretch" divider={<Divider />}>
              {notifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onDelete={handleDelete}
                />
              ))}
            </VStack>
          )}
          {notifications.length > 0 && (
            <Box p={3} textAlign="center">
              <Button size="sm" variant="link">
                View all notifications
              </Button>
            </Box>
          )}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationCenter;
