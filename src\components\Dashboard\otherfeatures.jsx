import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Flex,
  Icon
} from '@chakra-ui/react';
import { FiTrendingUp, FiUsers, FiDollarSign, FiShoppingCart } from 'react-icons/fi';
import SimpleChart from '../SimpleChart';

const MinimalDashboard = () => {
  return (
    <Box py={6}>
      <Container maxW="container.xl">
        <Heading size="lg" mb={4}>Dashboard</Heading>
        <Text color="gray.600" mb={6}>Welcome to your dashboard overview</Text>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
          <Card>
            <CardBody>
              <Flex justify="space-between" align="center">
                <Stat>
                  <StatLabel>Revenue</StatLabel>
                  <StatNumber>$23,500</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    23.36%
                  </StatHelpText>
                </Stat>
                <Box p={2} bg="blue.50" borderRadius="md">
                  <Icon as={FiDollarSign} boxSize={6} color="blue.500" />
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex justify="space-between" align="center">
                <Stat>
                  <StatLabel>New Users</StatLabel>
                  <StatNumber>452</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    12.05%
                  </StatHelpText>
                </Stat>
                <Box p={2} bg="green.50" borderRadius="md">
                  <Icon as={FiUsers} boxSize={6} color="green.500" />
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex justify="space-between" align="center">
                <Stat>
                  <StatLabel>Orders</StatLabel>
                  <StatNumber>1,257</StatNumber>
                  <StatHelpText>
                    <StatArrow type="decrease" />
                    3.05%
                  </StatHelpText>
                </Stat>
                <Box p={2} bg="purple.50" borderRadius="md">
                  <Icon as={FiShoppingCart} boxSize={6} color="purple.500" />
                </Box>
              </Flex>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Flex justify="space-between" align="center">
                <Stat>
                  <StatLabel>Growth</StatLabel>
                  <StatNumber>18.4%</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    5.14%
                  </StatHelpText>
                </Stat>
                <Box p={2} bg="orange.50" borderRadius="md">
                  <Icon as={FiTrendingUp} boxSize={6} color="orange.500" />
                </Box>
              </Flex>
            </CardBody>
          </Card>
        </SimpleGrid>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          <SimpleChart />

          <Card>
            <CardBody>
              <Heading size="md" mb={4}>User Activity</Heading>
              <Text>Charts and graphs will be added here.</Text>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <Heading size="md" mb={4}>Performance</Heading>
              <Text>Performance metrics will be displayed here.</Text>
            </CardBody>
          </Card>
        </SimpleGrid>
      </Container>
    </Box>
  );
};

export default MinimalDashboard;
