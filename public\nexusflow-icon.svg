<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" rx="128" fill="url(#paint0_linear)" />
  <path d="M256 128C185.307 128 128 185.307 128 256C128 326.693 185.307 384 256 384C326.693 384 384 326.693 384 256C384 185.307 326.693 128 256 128ZM256 352C203.065 352 160 308.935 160 256C160 203.065 203.065 160 256 160C308.935 160 352 203.065 352 256C352 308.935 308.935 352 256 352Z" fill="white" fill-opacity="0.9"/>
  <path d="M256 192C221.888 192 194.745 219.143 194.745 253.255C194.745 287.367 221.888 314.51 256 314.51C290.112 314.51 317.255 287.367 317.255 253.255C317.255 219.143 290.112 192 256 192Z" fill="white" fill-opacity="0.9"/>
  <path d="M384 128L320 192L384 256L448 192L384 128Z" fill="#4FD1C5" fill-opacity="0.9"/>
  <path d="M128 256L64 320L128 384L192 320L128 256Z" fill="#4FD1C5" fill-opacity="0.9"/>
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop stop-color="#3182CE"/>
      <stop offset="1" stop-color="#805AD5"/>
    </linearGradient>
  </defs>
</svg>
