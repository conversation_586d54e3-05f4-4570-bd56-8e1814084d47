import { useState, useEffect, useCallback } from 'react';
import {
  VStack, HStack, Text, Avatar, Box, IconButton,
  useColorModeValue, Icon, Badge, Skeleton,
  useToast
} from '@chakra-ui/react';
import { 
  FiUser, FiFile, FiGitMerge, FiMessageSquare,
  FiRefreshCw, FiFilter
} from 'react-icons/fi';
import DOMPurify from 'dompurify';

const ActivityFeed = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const toast = useToast();

  const sanitizeContent = useCallback((content) => {
    return DOMPurify.sanitize(content, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
      ALLOWED_ATTR: []
    });
  }, []);

  const fetchActivities = useCallback(async () => {
    try {
      setLoading(true);
      // Simulated API call - replace with actual API
      const response = await fetch('/api/activities');
      const data = await response.json();
      
      const sanitizedActivities = data.map(activity => ({
        ...activity,
        user: sanitizeContent(activity.user),
        action: sanitizeContent(activity.action),
        message: sanitizeContent(activity.message)
      }));

      setActivities(sanitizedActivities);
    } catch (error) {
      toast({
        title: "Error fetching activities",
        status: "error",
        duration: 3000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  }, [sanitizeContent, toast]);

  useEffect(() => {
    fetchActivities();
    
    // Set up WebSocket connection for real-time updates
    const ws = new WebSocket('wss://your-api/activities');
    
    ws.onmessage = (event) => {
      const newActivity = JSON.parse(event.data);
      setActivities(prev => [
        {
          ...newActivity,
          user: sanitizeContent(newActivity.user),
          action: sanitizeContent(newActivity.action),
          message: sanitizeContent(newActivity.message)
        },
        ...prev
      ]);
    };

    return () => ws.close();
  }, [fetchActivities, sanitizeContent]);

  const getBadgeColor = (type) => ({
    create: 'green',
    comment: 'blue',
    merge: 'purple',
    join: 'orange'
  }[type] || 'gray');

  const getIcon = (type) => ({
    create: FiFile,
    comment: FiMessageSquare,
    merge: FiGitMerge,
    join: FiUser
  }[type] || FiFile);

  if (loading) {
    return (
      <VStack spacing={4}>
        {[1, 2, 3].map(i => (
          <Skeleton key={i} height="60px" width="100%" borderRadius="md" />
        ))}
      </VStack>
    );
  }

  return (
    <Box
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="xl"
      p={4}
      boxShadow="lg"
    >
      <HStack justify="space-between" mb={4}>
        <Text fontSize="lg" fontWeight="semibold">Activity Feed</Text>
        <HStack>
          <IconButton
            icon={<FiFilter />}
            aria-label="Filter activities"
            size="sm"
            variant="ghost"
            onClick={() => setFilter(filter === 'all' ? 'unread' : 'all')}
          />
          <IconButton
            icon={<FiRefreshCw />}
            aria-label="Refresh activities"
            size="sm"
            variant="ghost"
            onClick={fetchActivities}
          />
        </HStack>
      </HStack>

      <VStack spacing={4} align="stretch">
        {activities.map((activity) => (
          <HStack
            key={activity.id}
            spacing={3}
            p={3}
            borderRadius="md"
            bg={useColorModeValue('gray.50', 'gray.700')}
            _hover={{ bg: useColorModeValue('gray.100', 'gray.600') }}
            transition="background 0.2s"
          >
            <Avatar
              size="sm"
              name={activity.user}
              src={activity.userAvatar}
              referrerPolicy="no-referrer"
            />
            <VStack align="start" spacing={0} flex={1}>
              <Text fontSize="sm" fontWeight="medium">
                {activity.user}
                <Text as="span" fontWeight="normal" ml={1}>
                  {activity.action}
                </Text>
              </Text>
              {activity.message && (
                <Text fontSize="sm" color="gray.500">
                  {activity.message}
                </Text>
              )}
              <Text fontSize="xs" color="gray.500">
                {activity.time}
              </Text>
            </VStack>
            <Badge
              colorScheme={getBadgeColor(activity.type)}
              variant="subtle"
              borderRadius="full"
              px={2}
            >
              <Icon as={getIcon(activity.type)} mr={1} />
              {activity.type}
            </Badge>
          </HStack>
        ))}
      </VStack>
    </Box>
  );
};

export default ActivityFeed;

