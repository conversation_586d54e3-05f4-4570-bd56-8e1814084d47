import {
  Container, Box, VStack, HStack, Heading, Avatar, Text, Button, Card, CardBody,
  Stack, FormControl, FormLabel, Input, SimpleGrid, Icon, Divider, useToast,
  Badge, Tabs, TabList, TabPanels, Tab, TabPanel, Progress, Textarea, Switch,
  IconButton, Tooltip, useColorModeValue, Modal, ModalOverlay, ModalContent,
  ModalHeader, ModalBody, ModalCloseButton, useDisclosure, Image, Flex, Link
} from '@chakra-ui/react'
import { 
  FiMail, FiPhone, FiMapPin, FiCalendar, FiEdit2, FiGithub, FiLinkedin,
  FiTwitter, FiUpload, FiActivity, FiAward, FiBook, FiBriefcase, FiCode,
  FiGlobe, FiHeart, FiTrendingUp, FiUsers, FiUser
} from 'react-icons/fi'
import { useState, useRef } from 'react'
import useAuthStore from '../store/authStore'

function UserProfile() {
  const { user } = useAuthStore()
  const toast = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const { isOpen, onOpen, onClose } = useDisclosure()
  const fileInputRef = useRef()
  const bgCard = useColorModeValue('white', 'gray.700')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const [profileData, setProfileData] = useState({
    name: user?.name || 'John Doe',
    role: 'Senior Software Engineer',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Senior Software Engineer with 8+ years of experience in full-stack development. Passionate about creating scalable solutions and mentoring junior developers.',
    github: 'github.com/johndoe',
    linkedin: 'linkedin.com/in/johndoe',
    twitter: '@johndoe',
    website: 'johndoe.dev'
  })

  const skills = [
    { name: 'React', level: 90, color: 'blue' },
    { name: 'Node.js', level: 85, color: 'green' },
    { name: 'TypeScript', level: 80, color: 'purple' },
    { name: 'Python', level: 75, color: 'yellow' },
    { name: 'AWS', level: 70, color: 'orange' },
    { name: 'Docker', level: 65, color: 'cyan' }
  ]

  const achievements = [
    {
      icon: FiAward,
      title: "Senior Developer Certification",
      date: "September 2023",
      description: "Achieved advanced certification in full-stack development"
    },
    {
      icon: FiCode,
      title: "100+ Projects Completed",
      date: "August 2023",
      description: "Successfully delivered over 100 client projects"
    },
    {
      icon: FiBriefcase,
      title: "Team Lead Promotion",
      date: "July 2023",
      description: "Promoted to lead a team of 5 developers"
    },
    {
      icon: FiBook,
      title: "Technical Blog Recognition",
      date: "June 2023",
      description: "Featured article in prominent tech publication"
    }
  ]

  const projects = [
    {
      name: 'E-Commerce Platform',
      description: 'Built a scalable e-commerce platform using React and Node.js',
      tech: ['React', 'Node.js', 'MongoDB'],
      role: 'Tech Lead'
    },
    {
      name: 'Analytics Dashboard',
      description: 'Developed real-time analytics dashboard for business metrics',
      tech: ['TypeScript', 'D3.js', 'AWS'],
      role: 'Senior Developer'
    },
    {
      name: 'Mobile Payment App',
      description: 'Led the development of a secure mobile payment application',
      tech: ['React Native', 'Firebase', 'Stripe'],
      role: 'Project Lead'
    }
  ]

  const handleEdit = () => {
    setIsEditing(false)
    toast({
      title: 'Profile Updated',
      description: 'Your changes have been saved successfully.',
      status: 'success',
      duration: 3000,
    })
  }

  const handlePhotoUpload = async (event) => {
    try {
      const file = event.target.files[0]
      if (!file) return
      
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid file type',
          description: 'Please upload an image file',
          status: 'error',
          duration: 3000,
        })
        return
      }

      // TODO: Implement actual file upload logic here
      // const formData = new FormData()
      // formData.append('avatar', file)
      // const response = await uploadUserAvatar(formData)

      toast({
        title: 'Photo Uploaded',
        description: 'Your profile photo has been updated.',
        status: 'success',
        duration: 3000,
      })
    } catch (error) {
      toast({
        title: 'Upload Failed',
        description: error.message,
        status: 'error',
        duration: 3000,
      })
    }
  }

  const StatCard = ({ icon, label, value }) => (
    <Card>
      <CardBody>
        <VStack>
          <Icon as={icon} boxSize={6} color="blue.500" />
          <Text fontWeight="bold" fontSize="xl">{value}</Text>
          <Text color="gray.500" fontSize="sm">{label}</Text>
        </VStack>
      </CardBody>
    </Card>
  )

  return (
    <Container maxW="container.xl" py={8}>
      <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={8}>
        {/* Left Column - Profile Overview */}
        <VStack spacing={8}>
          <Card w="full">
            <CardBody>
              <VStack spacing={6} align="center">
                <Box position="relative">
                  <Avatar 
                    size="2xl" 
                    name={profileData.name}
                    src={user?.avatar || "/default-avatar.png"}
                    border="4px solid"
                    borderColor="blue.400"
                  />
                  <Tooltip label="Upload new photo">
                    <IconButton
                      aria-label="Upload photo"
                      icon={<FiUpload />}
                      size="sm"
                      colorScheme="blue"
                      position="absolute"
                      bottom={0}
                      right={0}
                      rounded="full"
                      onClick={() => fileInputRef.current?.click()}
                    />
                  </Tooltip>
                  <input
                    type="file"
                    ref={fileInputRef}
                    hidden
                    accept="image/*"
                    onChange={handlePhotoUpload}
                  />
                </Box>

                <Box textAlign="center">
                  <Heading size="lg">{profileData.name}</Heading>
                  <Text color="gray.500" fontSize="lg">{profileData.role}</Text>
                  <HStack justify="center" mt={2} spacing={2}>
                    <Badge colorScheme="blue">Available for hire</Badge>
                    <Badge colorScheme="green">Remote</Badge>
                    <Badge colorScheme="purple">Mentor</Badge>
                  </HStack>
                </Box>

                <SimpleGrid columns={2} spacing={4} w="full">
                  <StatCard icon={FiUsers} label="Followers" value="2.4K" />
                  <StatCard icon={FiHeart} label="Following" value="1.2K" />
                  <StatCard icon={FiCode} label="Projects" value="156" />
                  <StatCard icon={FiTrendingUp} label="Impact" value="89%" />
                </SimpleGrid>
                
                <Divider />
                
                <VStack spacing={4} w="full">
                  <InfoRow icon={FiMail} label="Email" value={profileData.email} />
                  <InfoRow icon={FiPhone} label="Phone" value={profileData.phone} />
                  <InfoRow icon={FiMapPin} label="Location" value={profileData.location} />
                  <InfoRow icon={FiCalendar} label="Member Since" value="January 2023" />
                  <InfoRow icon={FiGlobe} label="Website" value={profileData.website} isLink />
                </VStack>

                <Stack direction="row" spacing={4}>
                  <SocialButton icon={FiGithub} label="GitHub" href={`https://${profileData.github}`} />
                  <SocialButton icon={FiLinkedin} label="LinkedIn" href={`https://${profileData.linkedin}`} />
                  <SocialButton icon={FiTwitter} label="Twitter" href={`https://twitter.com/${profileData.twitter}`} />
                </Stack>
              </VStack>
            </CardBody>
          </Card>
        </VStack>

        {/* Middle and Right Columns */}
        <Box gridColumn={{ base: "auto", lg: "2 / span 2" }}>
          <Tabs variant="enclosed">
            <TabList>
              <Tab><HStack><Icon as={FiUser} /><Text>Overview</Text></HStack></Tab>
              <Tab><HStack><Icon as={FiCode} /><Text>Skills</Text></HStack></Tab>
              <Tab><HStack><Icon as={FiAward} /><Text>Achievements</Text></HStack></Tab>
              <Tab><HStack><Icon as={FiBriefcase} /><Text>Projects</Text></HStack></Tab>
            </TabList>

            <TabPanels>
              {/* Overview Tab */}
              <TabPanel>
                <VStack spacing={6}>
                  <Card w="full">
                    <CardBody>
                      <VStack spacing={6} align="stretch">
                        <HStack justify="space-between">
                          <Heading size="md">About Me</Heading>
                          <Button
                            leftIcon={<FiEdit2 />}
                            size="sm"
                            onClick={() => setIsEditing(!isEditing)}
                          >
                            {isEditing ? 'Cancel' : 'Edit'}
                          </Button>
                        </HStack>
                        
                        <Textarea
                          value={profileData.bio}
                          onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
                          isReadOnly={!isEditing}
                          rows={4}
                        />

                        {isEditing && (
                          <Button colorScheme="blue" onClick={handleEdit}>
                            Save Changes
                          </Button>
                        )}
                      </VStack>
                    </CardBody>
                  </Card>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} w="full">
                    {projects.slice(0, 2).map((project, index) => (
                      <ProjectCard key={index} {...project} />
                    ))}
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              {/* Skills Tab */}
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  {skills.map((skill) => (
                    <Card key={skill.name}>
                      <CardBody>
                        <VStack align="stretch" spacing={2}>
                          <HStack justify="space-between">
                            <Text fontWeight="bold">{skill.name}</Text>
                            <Text>{skill.level}%</Text>
                          </HStack>
                          <Progress 
                            value={skill.level} 
                            colorScheme={skill.color}
                            rounded="full"
                            size="sm"
                          />
                        </VStack>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              </TabPanel>

              {/* Achievements Tab */}
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  {achievements.map((achievement, index) => (
                    <Card key={index} className="achievement-card">
                      <CardBody>
                        <HStack spacing={4}>
                          <Box p={3} bg="blue.50" rounded="full">
                            <Icon as={achievement.icon} boxSize={6} color="blue.500" />
                          </Box>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="bold">{achievement.title}</Text>
                            <Text fontSize="sm" color="gray.500">{achievement.date}</Text>
                            <Text fontSize="sm">{achievement.description}</Text>
                          </VStack>
                        </HStack>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              </TabPanel>

              {/* Projects Tab */}
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  {projects.map((project, index) => (
                    <ProjectCard key={index} {...project} />
                  ))}
                </SimpleGrid>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      </SimpleGrid>
    </Container>
  )
}

const InfoRow = ({ icon, label, value, isLink }) => (
  <HStack spacing={4} w="full">
    <Icon as={icon} color="gray.500" />
    <Box>
      <Text fontSize="sm" color="gray.500">{label}</Text>
      {isLink ? (
        <Link href={`https://${value}`} color="blue.500" isExternal>
          {value}
        </Link>
      ) : (
        <Text>{value}</Text>
      )}
    </Box>
  </HStack>
)

const SocialButton = ({ icon, label, href }) => (
  <IconButton
    as="a"
    href={href}
    target="_blank"
    aria-label={label}
    icon={<Icon as={icon} />}
    rounded="full"
    variant="ghost"
    className="social-icon"
    _hover={{
      bg: 'blue.50',
      color: 'blue.500',
      transform: 'translateY(-2px)'
    }}
  />
)

const ProjectCard = ({ name, description, tech, role }) => (
  <Card>
    <CardBody>
      <VStack align="start" spacing={3}>
        <Heading size="sm">{name}</Heading>
        <Text fontSize="sm">{description}</Text>
        <HStack spacing={2}>
          {tech.map((t, i) => (
            <Badge key={i} colorScheme="blue">{t}</Badge>
          ))}
        </HStack>
        <Text fontSize="sm" color="gray.500">Role: {role}</Text>
      </VStack>
    </CardBody>
  </Card>
)

export default UserProfile



