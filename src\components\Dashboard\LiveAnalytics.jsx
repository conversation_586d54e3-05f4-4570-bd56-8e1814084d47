import React from 'react';
import { Box, Heading, Flex, Text, useColorModeValue } from '@chakra-ui/react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const LiveAnalytics = () => {
  const data = [
    { time: '00:00', value: 2400 },
    { time: '04:00', value: 1398 },
    { time: '08:00', value: 9800 },
    { time: '12:00', value: 3908 },
    { time: '16:00', value: 4800 },
    { time: '20:00', value: 3800 },
    { time: '24:00', value: 4300 },
  ];

  return (
    <Box bg={useColorModeValue('white', 'gray.800')} p={4} borderRadius="lg" boxShadow="sm">
      <Heading size="md" mb={4}>Live Analytics</Heading>
      <Box h="300px">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="value" stroke="#3182ce" />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default LiveAnalytics;