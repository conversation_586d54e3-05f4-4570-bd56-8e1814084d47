export const dashboardStyles = {
  container: {
    maxW: "container.xl",
    py: 8
  },
  statCard: {
    transition: "transform 0.2s",
    _hover: {
      transform: "translateY(-4px)",
      shadow: "lg"
    }
  },
  card: {
    borderRadius: "xl",
    shadow: "base",
    transition: "all 0.2s",
    _hover: {
      shadow: "lg"
    }
  },
  tableContainer: {
    overflowX: "auto",
    maxH: "400px"
  },
  progressBar: {
    borderRadius: "full",
    size: "sm"
  },
  activityItem: {
    p: 3,
    borderRadius: "md",
    _hover: {
      bg: "gray.50",
      _dark: {
        bg: "gray.700"
      }
    }
  }
}
